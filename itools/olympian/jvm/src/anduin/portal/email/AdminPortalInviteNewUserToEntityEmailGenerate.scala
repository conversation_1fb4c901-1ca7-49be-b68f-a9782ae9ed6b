// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.email

import yamusca.context.{Context, Value}
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.email.topic.{EmailTopic, PortalInviteNewUserToEntity}
import anduin.id.entity.EntityId
import anduin.id.offering.OfferingId
import anduin.link.LinkGeneratorService
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.portal.CustomizedEntityInvitationEmailParams
import com.anduin.stargazer.service.email.{EmailPriority, EmailSenderHelper}
import com.anduin.stargazer.service.email.generate.GenerateSingleEmail
import com.anduin.stargazer.service.email.generate.common.{RedirectUrlTerm, Terms}
import stargazer.model.routing.DynamicAuthPage.Entity.Portal

final case class AdminPortalInviteNewUserToEntityEmailGenerate(
  userId: UserId,
  entityId: EntityId,
  entityAlias: String,
  customizedEmailParams: CustomizedEntityInvitationEmailParams
)(
  using val linkGeneratorService: LinkGeneratorService,
  userProfileService: UserProfileService
) extends GenerateSingleEmail
    with PortalEmailTemplate {

  override val priority: EmailPriority = EmailPriority.Medium

  override val topic: EmailTopic = PortalInviteNewUserToEntity()

  override protected def templateName: String = "portal/entity-invitation"

  override protected def getSenderAddress: Task[EmailAddress] = EmailSenderHelper.defaultSystemAddressTask

  override protected def getReceivers: Task[Seq[EmailAddress]] = {
    userProfileService.getEmailAddress(userId).map(Seq(_))
  }

  private val redirectUrlTerm: Terms = {
    RedirectUrlTerm(
      tag = "redirectUrl",
      pageTask = ZIO.attempt(Portal(entityId)),
      userId = Some(userId),
      offeringId = OfferingId.Gondor
    )
  }

  override protected def getAdditionalMapping: Task[Context] = {
    val mapping = Context("entityName" -> Value.of(entityAlias))
    for {
      terms <- baseTerms
      res <- getTemplateMapping(
        customizedEmailParams,
        mapping,
        terms
      )
    } yield res
  }

  override protected val baseTerms: Task[List[Terms]] = ZIO.attempt(List(redirectUrlTerm))
}
