// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.asyncapiv2.execution

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration

enum AsyncApiWorkflowQueue {
  case Fast
  case Medium
  case Heavy

  def getTimeout: FiniteDuration = {
    this match {
      case AsyncApiWorkflowQueue.Fast   => FiniteDuration(30, TimeUnit.SECONDS)
      case AsyncApiWorkflowQueue.Medium => FiniteDuration(5, TimeUnit.MINUTES)
      case AsyncApiWorkflowQueue.Heavy  => FiniteDuration(15, TimeUnit.MINUTES)
    }
  }

}
