// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.zapier.storage

import com.apple.foundationdb.record.{EndpointType, IndexScanType, TupleRange}

import anduin.fdb.record.FDBOperations
import anduin.fdb.record.model.{FDBTupleConverter, RecordIO, RecordTask}
import anduin.id.zapier.ZapId
import anduin.zapier.storage.ZapierStoreProvider.{*, given}
import anduin.model.common.user.UserId
import anduin.protobuf.integration.zapier.Zap

final case class ZapierStoreOperations(store: Store) {

  // Zap
  def createZap(model: Zap): RecordTask[Unit] = store.create(model).unit

  def getZap(id: ZapId): RecordTask[Zap] = store.get(id)

  def getOptZap(id: ZapId): RecordTask[Option[Zap]] = store.getOpt(id)

  def updateZap(
    id: ZapId
  )(
    fn: Zap => Zap
  ): RecordTask[Zap] = {
    for {
      model <- store.get(id)
      newModel <- RecordIO.succeed(fn(model))
      _ <- store.update(newModel)
    } yield newModel
  }

  def zapExist(id: ZapId): RecordTask[Boolean] = store.exist(id)

  def deleteZap(id: ZapId): RecordTask[Boolean] = store.delete(id)

  def getZapsByUserId(userId: UserId): RecordTask[Seq[Zap]] = {
    val prefix = FDBTupleConverter.userId.toTuple(userId)
    for {
      models <- store.scanIndexRecordsL(
        ZapierStoreProvider.zapByUserIdMapping,
        new TupleRange(
          prefix,
          prefix,
          EndpointType.PREFIX_STRING,
          EndpointType.PREFIX_STRING
        ),
        IndexScanType.BY_VALUE
      )
    } yield models
  }

  def getZapsByType(zapType: String): RecordTask[Seq[Zap]] = {
    val prefix = FDBTupleConverter.string.toTuple(zapType)
    for {
      models <- store.scanIndexRecordsL(
        ZapierStoreProvider.zapByTypeMapping,
        new TupleRange(
          prefix,
          prefix,
          EndpointType.PREFIX_STRING,
          EndpointType.PREFIX_STRING
        ),
        IndexScanType.BY_VALUE
      )
    } yield models
  }

  def getZapsByGroup(group: String): RecordTask[Seq[Zap]] = {
    val prefix = FDBTupleConverter.string.toTuple(group)
    for {
      models <- store.scanIndexRecordsL(
        ZapierStoreProvider.zapByGroupMapping,
        new TupleRange(
          prefix,
          prefix,
          EndpointType.PREFIX_STRING,
          EndpointType.PREFIX_STRING
        ),
        IndexScanType.BY_VALUE
      )
    } yield models
  }

}

object ZapierStoreOperations extends FDBOperations.Single[RecordEnum, ZapierStoreOperations](ZapierStoreProvider)
