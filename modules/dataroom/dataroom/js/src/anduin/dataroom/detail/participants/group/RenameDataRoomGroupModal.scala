// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.group

import design.anduin.components.button.Button
import design.anduin.components.field.Field
import design.anduin.components.modal.{Modal, ModalBody, ModalFooterWCancel}
import design.anduin.components.textbox.TextBox
import design.anduin.components.toast.Toast

import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.group.RenameDataRoomGroupParams
import anduin.id.dataroom.DataRoomGroupId

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.anduin.stargazer.client.utils.ZIOUtils

private[participants] final case class RenameDataRoomGroupModal(
  groupId: DataRoomGroupId,
  groupName: String,
  renderTarget: Callback => VdomNode,
  onClose: Callback
) {
  def apply(): VdomElement = RenameDataRoomGroupModal.component(this)
}

private[participants] object RenameDataRoomGroupModal {

  private type Props = RenameDataRoomGroupModal

  private final case class State(
    name: String,
    isBusy: Boolean
  )

  private class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomNode = {
      Modal(
        title = "Rename group",
        renderTarget = props.renderTarget,
        renderContent = renderContent(props, state),
        size = Modal.Size(Modal.Width.Px480)
      )()
    }

    private def renderContent(props: Props, state: State)(closeToggle: Callback) = {
      React.Fragment(
        ModalBody()(
          Field(
            label = Some("Name"),
            requirement = Field.Requirement.Required,
            id = Some("group-name")
          )(
            TextBox(
              value = state.name,
              onChange = newName => scope.modState(_.copy(name = newName)),
              placeholder = "Enter group name",
              id = Some("group-name"),
              isAutoFocus = true,
              unsafeTagMod = TagMod(
                ^.onFocus ==> { (e: ReactFocusEventFromInput) =>
                  Callback {
                    e.target.select()
                  }
                }
              )
            )()
          )
        ),
        ModalFooterWCancel(cancel = closeToggle >> props.onClose)(
          Button(
            style = Button.Style.Full(color = Button.Color.Primary, isBusy = state.isBusy),
            onClick = renameGroup(closeToggle >> props.onClose),
            isDisabled = state.name.isEmpty
          )("Rename")
        )
      )
    }

    private def renameGroup(onClose: Callback): Callback = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- Callback.when(state.name.nonEmpty)(
          scope.modState(
            _.copy(isBusy = true),
            ZIOUtils.toReactCallback {
              DataRoomEndpointClient
                .renameGroup(
                  RenameDataRoomGroupParams(
                    groupId = props.groupId,
                    name = state.name
                  )
                )
                .map(resp =>
                  scope.modState(
                    _.copy(isBusy = false),
                    resp.fold(
                      _ => Toast.errorCallback("Failed to rename group"),
                      _ => Toast.successCallback("Group renamed") >> onClose
                    )
                  )
                )
            }
          )
        )
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps(props =>
      State(
        name = props.groupName,
        isBusy = false
      )
    )
    .renderBackend[Backend]
    .componentDidUpdate(scope =>
      Callback.when(
        scope.prevProps.groupId != scope.currentProps.groupId || scope.prevProps.groupName != scope.currentProps.groupName
      )(
        scope.modState(_.copy(name = scope.currentProps.groupName))
      )
    )
    .build

}
