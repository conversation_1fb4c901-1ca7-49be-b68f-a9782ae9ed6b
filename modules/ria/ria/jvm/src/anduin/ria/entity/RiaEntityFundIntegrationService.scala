// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.ria.entity

import java.time.Instant

import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.fundsub.endpoint.lp.AdvisorCreateOrderInfo
import anduin.fundsub.endpoint.ria.EstablishRiaEntityLinkageParams
import anduin.fundsub.integration.FundSubExternalIntegrationService
import anduin.fundsub.integration.FundSubExternalIntegrationService.NotHavePermissionToGetLinkedRiaGroupException
import anduin.fundsub.utils.FundSubRiaPermissionUtils
import anduin.greylin.core.cdc.TypedEvent
import anduin.greylin.core.event.GreylinEventListener
import anduin.greylin.modelti.{SubscriptionOrder, SubscriptionOrderMember, SubscriptionOrderStatus}
import anduin.greylin.operation.{SubscriptionOrderMemberOperations, SubscriptionOrderOperations}
import anduin.greylin.{GreylinDataService, modelti}
import anduin.id.fundsub.ria.FundSubRiaGroupId
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.id.ria.{RiaEntityId, RiaFundGroupId}
import anduin.id.role.portal.PortalSectionId
import anduin.kafka.KafkaService
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.RiaFundGroupIdFactory
import anduin.model.user.FullName
import anduin.portaluser.{ExecutiveAdmin, PortalUserService}
import anduin.protobuf.ria.advisor.entity.{InvitedByFund, RiaEntityFundAdvisorModel}
import anduin.protobuf.ria.email.RiaEmail.{RiaEntityEmailType, RiaFundGroupEmailType}
import anduin.protobuf.ria.entity.fund.RiaFundGroupModel
import anduin.protobuf.ria.entity.relation.user.RiaEntityUserRelationModel
import anduin.protobuf.ria.{RiaEntityFundAdvisorRole, RiaEntityUserRole}
import anduin.rebac.RebacStoreOperation
import anduin.ria.email.RiaEmailService
import anduin.ria.endpoints.*
import anduin.ria.entity.RiaEntityService.{AdvisorRaw, LinkedFundRaw, RiaEntityRaw, RiaUserRaw}
import anduin.ria.entity.advisor.RiaEntityFundAdvisorStoreOperations
import anduin.ria.entity.advisor.RiaEntityFundAdvisorStoreProvider.RiaEntityFundAdvisorPrimaryKey
import anduin.ria.entity.emaildomain.RiaEntityEmailDomainRelationStoreOperations
import anduin.ria.entity.fund.{RiaFundGroupService, RiaFundGroupStoreOperations}
import anduin.ria.entity.user.RiaEntityUserRelationStoreOperations
import anduin.ria.entity.user.RiaEntityUserRelationStoreProvider.RiaEntityUserRelationPrimaryKey
import anduin.ria.exception.RiaException.{
  GeneralValidationException,
  InvalidFundGroupRoleException,
  UserNotInRiaFundGroupException
}
import anduin.ria.order.entity.RiaEntityOrderRelationStoreOperations
import anduin.ria.permission.RiaPermissionService
import anduin.ria.utils.{RiaEntityFundAdvisorRoleUtils, RiaEntityUserRoleUtils}
import anduin.service.{AuthenticatedRequestContext, GeneralServiceException}
import anduin.tapir.endpoint.EmptyResponse
import anduin.team.TeamServiceParams.{
  AcceptInviteParams,
  CreateNewTeamParams,
  InviteAndCreateUserParams,
  InviteUserParams
}
import anduin.team.{TeamService, TeamServiceUtils}
import com.anduin.stargazer.service.utils.ZIOUtils

/** This service depend on both FundSubExternalIntegrationService and RiaEntityService to implement cross app
  * integrations from ria to fund sub
  */
final case class RiaEntityFundIntegrationService(
  riaEntityService: RiaEntityService,
  riaFundGroupService: RiaFundGroupService,
  portalUserService: PortalUserService,
  teamService: TeamService,
  riaPermissionService: RiaPermissionService,
  riaEmailService: RiaEmailService,
  kafkaService: KafkaService
)(
  using fundSubExternalIntegrationService: FundSubExternalIntegrationService,
  greylinDataService: GreylinDataService,
  userProfileService: UserProfileService,
  executiveAdmin: ExecutiveAdmin
) extends GreylinEventListener {

  ////////////////////////////////////////////////////////
  // Greylin event listener
  ////////////////////////////////////////////////////////

  override val groupName: String = "ria-entity-fund-integration-service-consumer"

  override val inputs: List[INPUT] = List(
    registerInput(modelti.SubscriptionOrder -> handleSubscriptionOrderEvent),
    registerInput(modelti.SubscriptionOrderMember -> handleSubscriptionOrderMemberEvent)
  )

  private def handleSubscriptionOrderEvent(event: TypedEvent[SubscriptionOrder]): Task[Unit] = {
    event match {
      case TypedEvent.UpdateEvent(oldData, newData) =>
        ZIOUtils.when(
          oldData.status != SubscriptionOrderStatus.Removed &&
            newData.status == SubscriptionOrderStatus.Removed
        ) {
          val orderId = newData.id
          for {
            _ <- ZIO.logInfo(s"Handling fund sub order deletion event $orderId")
            fundGroupOrderBelongToOpt <- RiaEntityService.getFundGroupOrderBelongTo(orderId)
            _ <- ZIOUtils.traverseOptionUnit(fundGroupOrderBelongToOpt) { fundGroupOrderBelongTo =>
              unlinkOrderUnsafe(
                riaFundGroupId = fundGroupOrderBelongTo.id,
                orderId = orderId
              )
            }
          } yield ()
        }
      case _ => ZIO.unit
    }
  }

  private def handleSubscriptionOrderMemberEvent(event: TypedEvent[SubscriptionOrderMember]): Task[Unit] = {
    event match {
      case TypedEvent.InsertEvent(data) =>
        for {
          _ <- ZIO.logInfo(s"Handling fund sub order member insert event ${data.subscriptionOrderId}")
          fundGroupOrderBelongToOpt <- RiaEntityService.getFundGroupOrderBelongTo(data.subscriptionOrderId)
          _ <- fundGroupOrderBelongToOpt.fold(
            convertOrderToFundGroupShouldBelongTo(data.subscriptionOrderId, Option(data.userId))
          ) { fundGroupOrderBelongTo =>
            for {
              shouldInviteAdvisor <- getValidOrderMembersToBeFundGroupMember(
                riaFundGroupId = fundGroupOrderBelongTo.id,
                ordersAndSelectedMemberToValidate = Map(data.subscriptionOrderId -> Option(data.userId))
              ).map(_.headOption.exists(_._2.nonEmpty))

              _ <- ZIO.when(shouldInviteAdvisor) {
                for {
                  _ <- inviteRiaFundAdvisorUnsafe(
                    params = InviteRiaEntityFundAdvisorParams(
                      riaFundGroupId = fundGroupOrderBelongTo.id,
                      inviteeUsersInfo = Seq(
                        InviteRiaEntityFundAdvisorInfo(
                          inviteeUser = Right(data.userId),
                          fundGroupRole = RiaEntityFundAdvisorRole.AdvisorMember
                        )
                      )
                    ),
                    actor = None,
                    isAllowReInvite = true
                  )
                  _ <- riaEntityService.createRiaOrderRelationsUnsafe(
                    orderId = data.subscriptionOrderId,
                    riaFundGroupId = fundGroupOrderBelongTo.id,
                    advisorUserIds = Seq(data.userId)
                  )
                } yield ()
              }
            } yield ()
          }
        } yield ()
      case TypedEvent.UpdateEvent(_, _) => ZIO.unit
      case TypedEvent.DeleteEvent(data) =>
        val orderId = data.subscriptionOrderId
        for {
          _ <- ZIO.logInfo(s"Handling fund sub order member deletion event $orderId")
          riaEntityIdOpt <- FDBRecordDatabase.transact(RiaEntityOrderRelationStoreOperations.Production) { ops =>
            ops.getByOrder(orderId).map(_.map(_.riaEntityId))
          }
          _ <- ZIOUtils.traverseOptionUnit(riaEntityIdOpt) { riaEntityId =>
            for {
              given RebacStoreOperation <- riaPermissionService.getRiaEntityRebacStore(riaEntityId)
              _ <- riaPermissionService.removeRiaUserOrderRelationsR(
                orderId = orderId,
                userId = data.userId
              )
            } yield ()
          }
        } yield ()
    }
  }

  ////////////////////////////////////////////////////////
  // Fund integration
  ////////////////////////////////////////////////////////

  def createAndLinkRiaEntityFromFundInvitation(
    params: CreateAndLinkRiaEntityFromFundInvitationParams,
    actor: UserId
  ): Task[CreateAndLinkRiaEntityFromFundInvitationResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is creating RIA entity ${params.name}")

      // validate permission
      given RebacStoreOperation <- fundSubExternalIntegrationService.getFundSubRebacStore(
        params.fundSubRiaGroupToLink.parent
      )
      hasPermissionToEstablishRiaEntityLinkage <- FundSubRiaPermissionUtils.checkPermissionToLinkRiaEntityR(
        fundSubRiaGroupId = params.fundSubRiaGroupToLink,
        userId = actor
      )

      isRiaFundGroupLinked <- fundSubExternalIntegrationService
        .getLinkedRiaFundGroupIdForFundGroup(
          fundSubRiaGroupId = params.fundSubRiaGroupToLink,
          actor = actor
        )
        .map(_.nonEmpty)

      _ <- ZIOUtils.validate(hasPermissionToEstablishRiaEntityLinkage && !isRiaFundGroupLinked) {
        GeneralServiceException(
          s"$actor does not have permission to create RIA entity and establish fund entity linkage"
        )
      }

      entityEmailDomains <- ZIO.when(params.creatWithEmailDomain) {
        userProfileService.getEmailAddress(actor).map(_.domain.value)
      }
      enableAutoAddWithEmailDomain = entityEmailDomains.nonEmpty

      // create ria entity
      riaEntityId <- riaEntityService.createRiaEntityUnsafe(
        name = params.name,
        emailDomains = entityEmailDomains.toSeq,
        serviceAccountEmail = None,
        enableAutoAddWithEmailDomain = enableAutoAddWithEmailDomain,
        actor = actor
      )

      // invite creator to be entity admin
      _ <- inviteRiaUserUnsafe(
        params = InviteRiaUserParams(
          riaEntityId = riaEntityId,
          riaUserDetails = Seq(
            InviteRiaUserDetail(
              user = Right(actor),
              role = RiaEntityUserRole.EntityAdmin
            )
          )
        ),
        actor = Option(actor),
        shouldSendEmail = false
      )

      _ <- riaEmailService.sendRiaEntityEmail(
        riaEntityId = riaEntityId,
        emailType = RiaEntityEmailType.RiaEntityCreated,
        receiver = actor,
        actor = Option(actor)
      )

      // create fund group
      fundLinkageResponse <- establishFundLinkage(
        params = EstablishFundLinkageParams(
          riaEntityId = riaEntityId,
          fundSubRiaGroupToLink = params.fundSubRiaGroupToLink
        ),
        actor = actor
      )

    } yield CreateAndLinkRiaEntityFromFundInvitationResponse(
      riaEntityId = riaEntityId,
      riaFundGroupId = fundLinkageResponse.riaFundGroupId
    )
  }

  private def inviteRiaUserUnsafe(
    params: InviteRiaUserParams,
    actor: Option[UserId],
    isAllowReInvite: Boolean = false,
    shouldSendEmail: Boolean = true
  ): Task[InviteRiaUserResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is inviting RIA user to RIA entity ${params.riaEntityId}")

      riaEntityModel <- RiaEntityService.validateRiaEntityId(params.riaEntityId)

      // validate users not exist in ria entity
      userIdAndStatusInEntityMap <- ZIO
        .foreach(params.riaUserDetails) { riaUserDetail =>
          for {
            riaUserUserIdOpt <- riaUserDetail.user match {
              case Left(riaUserInfo)    => userProfileService.getOptUserIdFromEmailAddress(riaUserInfo.email)
              case Right(riaUserUserId) => ZIO.succeed(Option(riaUserUserId))
            }
            isInRiaEntity <- riaUserUserIdOpt.fold(ZIO.succeed(false)) { riaUserUserId =>
              FDBRecordDatabase
                .transact(RiaEntityUserRelationStoreOperations.Production)(
                  _.getByRiaEntityAndUser(params.riaEntityId, riaUserUserId)
                )
                .map(_.nonEmpty)
            }
          } yield riaUserUserIdOpt.map(_ -> isInRiaEntity)
        }
        .map(_.flatten.toMap)

      userIdsAlreadyExistInEntity = userIdAndStatusInEntityMap.filter(_._2).keys.toSet
      _ <- ZIO.when(!isAllowReInvite) {
        ZIOUtils.validate(userIdsAlreadyExistInEntity.isEmpty) {
          GeneralValidationException(
            s"One of user in $userIdsAlreadyExistInEntity is already in RIA entity ${params.riaEntityId}"
          )
        }
      }

      executiveAdmin <- executiveAdmin.userId
      riaUsers <- ZIOUtils.foreachParN(4)(params.riaUserDetails) { riaUserDetail =>
        val riaUser = riaUserDetail.user
        for {
          // add user to team
          riaUserUserId <- (riaUser match {
            case Left(riaUserInfo) =>
              teamService
                .inviteAndCreateUserIgnoreReInvite(
                  InviteAndCreateUserParams(
                    inviter = actor.getOrElse(executiveAdmin),
                    inviteeEmail = riaUserInfo.email,
                    inviteeFullName = Option(
                      FullName(
                        firstName = riaUserInfo.firstName,
                        lastName = riaUserInfo.lastName
                      )
                    ),
                    teamId = riaEntityModel.teamId,
                    skipPermission = true
                  )
                )

            case Right(riaUserUserId) =>
              teamService.inviteUserIgnoreReInvite(
                InviteUserParams(
                  inviter = actor.getOrElse(executiveAdmin),
                  invitee = riaUserUserId,
                  teamId = riaEntityModel.teamId,
                  skipPermission = true
                )
              )
          }).map(TeamServiceUtils.getUserIdFromMemberState)

          // Add/update user to ria entity
          _ <- updateRiaEntityUserRoleUnsafe(
            riaEntityId = params.riaEntityId,
            toUpdateUser = riaUserUserId,
            role = riaUserDetail.role,
            actor = actor,
            shouldSendEmail = false
          )

          _ <- ZIO.when(shouldSendEmail && !userIdsAlreadyExistInEntity.contains(riaUserUserId)) {
            riaEmailService.sendRiaEntityEmail(
              riaEntityId = params.riaEntityId,
              emailType = RiaEntityEmailType.RiaEntityUserInvited,
              receiver = riaUserUserId,
              actor = actor
            )
          }

        } yield (riaUserUserId, riaUserDetail.role)
      }

    } yield InviteRiaUserResponse(riaUsers.map(_._1))
  }

  def inviteRiaUser(
    params: InviteRiaUserParams,
    actor: UserId
  ): Task[InviteRiaUserResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is inviting RIA user ${params.riaUserDetails} to RIA entity ${params.riaEntityId}")
      _ <- ZIOUtils.validate(params.riaUserDetails.nonEmpty) {
        GeneralValidationException("Must provide at least one user")
      }
      _ <- RiaEntityService.validateRiaEntityId(params.riaEntityId)

      isAnduinAdmin <- portalUserService.hasWritePermission(
        userId = actor,
        sectionId = PortalSectionId.Ria
      )

      given RebacStoreOperation <- riaPermissionService.getRiaEntityRebacStore(params.riaEntityId)

      _ <- ZIO.when(!isAnduinAdmin) {
        ZIO.foreach(params.riaUserDetails.map(_.role).distinct) { riaUserRole =>
          riaPermissionService.validateUserCanInviteEntityUserR(
            riaEntityId = params.riaEntityId,
            entityUserRoleToInvite = riaUserRole,
            userId = actor
          )
        }
      }

      res <- inviteRiaUserUnsafe(params, Option(actor))
    } yield res
  }

  def inviteRiaFundAdvisor(
    params: InviteRiaEntityFundAdvisorParams,
    actor: UserId,
    invitedByFundOpt: Option[InvitedByFund] = None
  ): Task[InviteRiaEntityFundAdvisorResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is inviting fund advisor to RIA entity ${params.riaFundGroupId}")
      _ <- ZIOUtils.validate(params.inviteeUsersInfo.nonEmpty) {
        GeneralValidationException("Must provide at least one invitee")
      }

      // validate user has permission to invite user to entity
      given RebacStoreOperation <- riaPermissionService.getRiaEntityRebacStore(params.riaFundGroupId.parent)
      _ <- ZIO.foreach(
        params.inviteeUsersInfo.map { info =>
          info.fundGroupRole -> info.entityRole
        }.distinct
      ) { case (fundGroupRole, entityRoleOpt) =>
        for {
          entityRoleToInvite <- RiaEntityService.validateRiaEntityUserRoleWithRiaFundGroupRole(
            riaEntityFundGroupRole = fundGroupRole,
            riaEntityUserRoleOpt = entityRoleOpt
          )
          _ <- riaPermissionService.validateUserCanInviteEntityUserR(
            riaEntityId = params.riaFundGroupId.parent,
            entityUserRoleToInvite = entityRoleToInvite,
            userId = actor
          )
        } yield ()
      }

      // validate user has permission to invite user to fund group
      _ <- riaPermissionService.validateUserCanInviteFundGroupUserR(
        riaFundGroupId = params.riaFundGroupId,
        userId = actor
      )

      _ <- inviteRiaFundAdvisorUnsafe(
        params = params,
        actor = Option(actor),
        invitedByFundOpt = invitedByFundOpt
      )

    } yield InviteRiaEntityFundAdvisorResponse()
  }

  private def inviteRiaFundAdvisorUnsafe(
    params: InviteRiaEntityFundAdvisorParams,
    actor: Option[UserId],
    invitedByFundOpt: Option[InvitedByFund] = None,
    shouldSendEmail: Boolean = true,
    isAllowReInvite: Boolean = false
  ): Task[InviteRiaEntityFundAdvisorResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is unsafe inviting fund advisor to RIA entity ${params.riaFundGroupId}")

      riaFundGroupModel <- RiaEntityService.validateRiaFundGroupId(params.riaFundGroupId)
      riaEntityModel <- RiaEntityService.validateRiaEntityId(params.riaFundGroupId.parent)
      // validate users not exist in ria fund group
      inviteeUsersInfo <- ZIO.foreach(params.inviteeUsersInfo) { riaAdvisorDetail =>
        for {
          toInviteEntityRole <- RiaEntityService.validateRiaEntityUserRoleWithRiaFundGroupRole(
            riaEntityFundGroupRole = riaAdvisorDetail.fundGroupRole,
            riaEntityUserRoleOpt = riaAdvisorDetail.entityRole
          )
        } yield (riaAdvisorDetail.inviteeUser, toInviteEntityRole, riaAdvisorDetail.fundGroupRole)
      }

      riaAdvisorsAlreadyExistInFundGroup <- ZIO
        .foreach(params.inviteeUsersInfo) { inviteeUserInfo =>
          for {
            riaAdvisorUserIdOpt <- inviteeUserInfo.inviteeUser match {
              case Left(riaAdvisorInfo)    => userProfileService.getOptUserIdFromEmailAddress(riaAdvisorInfo.email)
              case Right(riaAdvisorUserId) => ZIO.succeed(Option(riaAdvisorUserId))
            }
            userIdAlreadyExistInFundGroupOpt <- ZIOUtils
              .traverseOption(riaAdvisorUserIdOpt) { riaAdvisorUserId =>
                FDBRecordDatabase
                  .transact(RiaEntityFundAdvisorStoreOperations.Production)(
                    _.getByRiaFundGroupAndAdvisor(params.riaFundGroupId, riaAdvisorUserId)
                  )
              }
              .map(_.flatten.map(_.advisor))
          } yield userIdAlreadyExistInFundGroupOpt
        }
        .map(_.flatten.toSet)

      _ <- ZIO.when(!isAllowReInvite) {
        ZIOUtils.validate(riaAdvisorsAlreadyExistInFundGroup.isEmpty) {
          GeneralValidationException(
            s"One of user in $riaAdvisorsAlreadyExistInFundGroup is already in RIA fund group ${params.riaFundGroupId}"
          )
        }
      }

      _ <- ZIOUtils
        .foreachParN(4)(inviteeUsersInfo) {
          case (
                inviteeUser,
                inviteeEntityRole,
                toInviteFundGroupRole
              ) =>
            for {
              // If user already exist, just update role
              addedRiaUserOpt <- inviteRiaUserUnsafe(
                params = InviteRiaUserParams(
                  riaEntityId = params.riaFundGroupId.parent,
                  riaUserDetails = Seq(
                    InviteRiaUserDetail(
                      user = inviteeUser,
                      role = inviteeEntityRole
                    )
                  )
                ),
                actor = actor,
                isAllowReInvite = true,
                shouldSendEmail = false
              )
                .map(_.userIds.headOption)

              _ <- ZIOUtils.traverseOption(addedRiaUserOpt) { addedRiaUser =>
                for {
                  _ <- updateRiaFundGroupAdvisorRoleUnsafe(
                    riaFundGroupId = riaFundGroupModel.id,
                    toUpdateAdvisor = addedRiaUser,
                    role = toInviteFundGroupRole,
                    actor = actor,
                    shouldSendEmail = false,
                    invitedByFundOpt = invitedByFundOpt
                  )
                  _ <- fundSubExternalIntegrationService.advisorJoinFundIfPreviouslyInvited(
                    fundSubRiaGroupId = riaFundGroupModel.fundSubRiaGroupId,
                    entityName = riaEntityModel.name,
                    actor = addedRiaUser
                  )
                  _ <- ZIO.when(shouldSendEmail && !riaAdvisorsAlreadyExistInFundGroup.contains(addedRiaUser)) {
                    riaEmailService.sendRiaFundGroupEmail(
                      riaFundGroupId = params.riaFundGroupId,
                      emailType = RiaFundGroupEmailType.RiaFundGroupUserInvited,
                      receiver = addedRiaUser,
                      actor = actor
                    )
                  }
                } yield ()
              }
            } yield ()
        }

    } yield InviteRiaEntityFundAdvisorResponse()
  }

  /** This function belong to this service because it needs to call getFundName from fund sub */
  def getRiaEntityUnsafe(
    ids: Seq[(RiaEntityId, Option[Seq[RiaFundGroupId]])]
  ): Task[Seq[RiaEntity]] = {
    for {
      riaEntitiesRaw <- ZIOUtils.foreachParN(4)(ids)(riaEntityService.getRiaEntityRawUnsafe)
      riaEntities <- RiaEntityRaw.refine(riaEntitiesRaw)
    } yield riaEntities
  }

  def getAccessibleRiaEntity(
    params: GetAccessibleRiaEntitiesParams,
    actor: UserId
  ): Task[GetAccessibleRiaEntitiesResponse] = {
    for {
      _ <- ZIO.logInfo(s"Get accessible RIA entities for $actor")

      riaEntitiesRaw <- riaEntityService.getAccessibleRiaEntityRaw(params.getBy, actor)
      riaEntities <- RiaEntityRaw.refine(riaEntitiesRaw)

    } yield GetAccessibleRiaEntitiesResponse(riaEntities)
  }

  def getAccessibleRiaEntityUsers(
    params: GetAccessibleRiaEntityUsersParams,
    actor: UserId
  ): Task[GetAccessibleRiaEntityUsersResponse] = {
    for {
      _ <- ZIO.logInfo(s"Get accessible RIA entity users for $actor")
      riaUserRaws <- riaEntityService
        .getAccessibleRiaEntityRaw(
          getBy = Option(Left(params.riaEntityId)),
          actor = actor
        )
        .map(_.headOption.fold(Seq[RiaUserRaw]())(_.users))

      accessibleUsers <- RiaUserRaw.refine(riaUserRaws)
    } yield GetAccessibleRiaEntityUsersResponse(accessibleUsers)
  }

  def getAccessibleLinkedFunds(
    params: GetAccessibleLinkedFundsParams,
    actor: UserId
  ): Task[GetAccessibleLinkedFundsResponse] = {
    for {
      _ <- ZIO.logInfo(s"Get accessible linked funds in RIA entity ${params.getBy} for $actor")

      linkedFundRaws <- riaEntityService
        .getAccessibleRiaEntityRaw(
          getBy = Option(params.getBy),
          actor = actor
        )
        .map(_.headOption.fold(Seq[LinkedFundRaw]())(_.linkedFunds))

      accessibleLinkedFunds <- LinkedFundRaw.refine(linkedFundRaws)
    } yield GetAccessibleLinkedFundsResponse(
      linkedFunds = accessibleLinkedFunds
    )
  }

  def getAccessibleLinkedFundAdvisors(
    params: GetAccessibleLinkedFundAdvisorsParams,
    actor: UserId
  ): Task[GetAccessibleLinkedFundAdvisorsResponse] = {
    for {
      _ <- ZIO.logInfo(s"Get accessible advisors in fund ${params.riaFundGroupId} for $actor")

      advisorRaws <- riaEntityService
        .getAccessibleRiaEntityRaw(
          getBy = Option(Right(params.riaFundGroupId)),
          actor = actor
        )
        .map(_.headOption.fold(Seq[AdvisorRaw]())(_.linkedFunds.flatMap(_.advisors)))

      accessibleAdvisors <- AdvisorRaw.refine(advisorRaws)

    } yield GetAccessibleLinkedFundAdvisorsResponse(
      advisors = accessibleAdvisors
    )
  }

  def getRiaEntityFromAdminPortal(
    params: GetRiaEntityFromAdminPortalParams,
    actor: UserId
  ): Task[GetRiaEntityFromAdminPortalResponse] = {
    for {
      _ <- ZIO.logInfo(s"Get RIA entity ${params.ids.mkString(", ")}")
      _ <- portalUserService.validateReadPermission(
        userId = actor,
        sectionId = PortalSectionId.Ria
      )
      toGetRiaEntityIds <-
        if (params.ids.isEmpty) {
          FDBRecordDatabase.transact(RiaEntityStoreOperations.Production)(_.getAll).map(_.map(_.id))
        } else {
          ZIO.succeed(params.ids)
        }
      riaEntities <- getRiaEntityUnsafe(toGetRiaEntityIds.map(_ -> None))
    } yield GetRiaEntityFromAdminPortalResponse(riaEntities)
  }

  /** shouldDegradeRole: if true, then role can be updated to lower level.
    */
  private def updateRiaEntityUserRoleUnsafe(
    riaEntityId: RiaEntityId,
    toUpdateUser: UserId,
    role: RiaEntityUserRole,
    actor: Option[UserId],
    shouldDegradeRole: Boolean = false,
    shouldSendEmail: Boolean = true
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor is updating RIA user $toUpdateUser role to $role")
      now <- ZIO.succeed(Instant.now())
      toBecomeRole <- FDBRecordDatabase.transact(RiaEntityUserRelationStoreOperations.Production) { ops =>
        for {
          oldModelOpt <- ops.getByRiaEntityAndUser(riaEntityId, toUpdateUser)

          toBecomeRole = oldModelOpt.fold(role) { oldModel =>
            if (
              shouldDegradeRole ||
              RiaEntityUserRoleUtils.getPermissionLevel(oldModel.role) <
                RiaEntityUserRoleUtils.getPermissionLevel(role)
            ) {
              role
            } else {
              oldModel.role
            }
          }

          _ <- ops.upsert(
            key = RiaEntityUserRelationPrimaryKey(
              riaEntityId = riaEntityId,
              userId = toUpdateUser
            ),
            default = RiaEntityUserRelationModel(
              riaEntityId = riaEntityId,
              userId = toUpdateUser,
              role = role,
              createdAt = Option(now),
              createdBy = actor
            ),
            updateFn = _.copy(role = toBecomeRole)
          )
        } yield toBecomeRole
      }

      given RebacStoreOperation <- riaPermissionService.getRiaEntityRebacStore(riaEntityId)

      // remove old ria user permission
      _ <- riaPermissionService.removeRiaEntityUserRoleRelationsR(
        riaEntityId = riaEntityId,
        riaEntityUserId = toUpdateUser
      )

      // grant new ria user permission
      _ <- riaPermissionService.createRiaEntityUserRoleRelationsR(
        riaEntityId = riaEntityId,
        riaEntityUsers = Seq((toUpdateUser, toBecomeRole))
      )

      _ <- ZIO.when(shouldSendEmail) {
        riaEmailService.sendRiaEntityEmail(
          riaEntityId = riaEntityId,
          emailType = RiaEntityEmailType.RiaEntityUserRoleUpdated,
          receiver = toUpdateUser,
          actor = actor
        )
      }

    } yield ()
  }

  def updateRiaEntityUserRole(
    params: UpdateRiaEntityUserRoleParams,
    actor: UserId,
    shouldSendEmail: Boolean = true
  ): Task[UpdateRiaEntityUserRoleResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is updating RIA entity user ${params.toUpdateUser} role to ${params.toUpdateRole}")

      actorRiaUserRole <- RiaEntityService.validateUserInRiaEntity(params.riaEntityId, actor).map(_.role)
      currentToUpdateUserRole <- RiaEntityService
        .validateUserInRiaEntity(params.riaEntityId, params.toUpdateUser)
        .map(_.role)

      _ <- actorRiaUserRole match {
        case RiaEntityUserRole.EntityMember | RiaEntityUserRole.Unrecognized(_) =>
          ZIO.fail(GeneralValidationException("Entity member cannot update role"))
        case RiaEntityUserRole.EntityManager =>
          for {
            _ <- ZIOUtils.validateNot(currentToUpdateUserRole.isEntityAdmin) {
              GeneralValidationException(
                "Entity manager cannot update role of an admin role user"
              )
            }
            _ <- ZIOUtils.validateNot(params.toUpdateRole == RiaEntityUserRole.EntityAdmin) {
              GeneralValidationException(
                "Entity manager cannot update role of another advisor to admin"
              )
            }
          } yield ()
        case RiaEntityUserRole.EntityAdmin => ZIO.unit
      }

      _ <- updateRiaEntityUserRoleUnsafe(
        riaEntityId = params.riaEntityId,
        toUpdateUser = params.toUpdateUser,
        role = params.toUpdateRole,
        actor = Option(actor),
        shouldSendEmail = shouldSendEmail,
        shouldDegradeRole = true
      )

      fundsToUpdateUserInAsAdmin <- FDBRecordDatabase
        .transact(RiaEntityFundAdvisorStoreOperations.Production)(
          _.getByRoleAndAdvisor(
            RiaEntityFundAdvisorRole.AdvisorAdmin,
            params.toUpdateUser
          )
        )

      _ <- ZIO.when(params.toUpdateRole.isEntityMember) {
        ZIO.foreach(fundsToUpdateUserInAsAdmin) { fund =>
          updateRiaFundGroupAdvisorRole(
            params = UpdateRiaFundGroupAdvisorRoleParams(
              riaFundGroupId = fund.riaFundGroupId,
              toUpdateAdvisor = params.toUpdateUser,
              toUpdateRole = RiaEntityFundAdvisorRole.AdvisorMember
            ),
            actor = actor,
            shouldSendEmail = false
          )
        }
      }

    } yield UpdateRiaEntityUserRoleResponse()
  }

  /** shouldDegradeRole: if true, then role can be updated to lower level.
    */
  private def updateRiaFundGroupAdvisorRoleUnsafe(
    riaFundGroupId: RiaFundGroupId,
    toUpdateAdvisor: UserId,
    role: RiaEntityFundAdvisorRole,
    actor: Option[UserId],
    shouldDegradeRole: Boolean = false,
    shouldSendEmail: Boolean = true,
    invitedByFundOpt: Option[InvitedByFund] = None
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor is updating advisor $toUpdateAdvisor role to $role in $riaFundGroupId")
      now <- ZIO.succeed(Instant.now())

      riaFundGroupModel <- RiaEntityService.validateRiaFundGroupId(riaFundGroupId)
      toUpdateUserEntityRole <- RiaEntityService
        .validateUserInRiaEntity(riaFundGroupId.parent, toUpdateAdvisor)
        .map(_.role)

      toBecomeRole <- FDBRecordDatabase.transact(RiaEntityFundAdvisorStoreOperations.Production) { ops =>
        for {
          oldModelOpt <- ops.getByRiaFundGroupAndAdvisor(riaFundGroupId, toUpdateAdvisor)
          toBecomeRole = oldModelOpt.fold(role) { oldModel =>
            if (
              shouldDegradeRole ||
              RiaEntityFundAdvisorRoleUtils.getPermissionLevel(oldModel.role) <
                RiaEntityFundAdvisorRoleUtils.getPermissionLevel(role)
            ) {
              role
            } else {
              oldModel.role
            }
          }

        } yield toBecomeRole
      }

      _ <- ZIO.when(toBecomeRole.isAdvisorAdmin && toUpdateUserEntityRole.isEntityMember) {
        updateRiaEntityUserRoleUnsafe(
          riaEntityId = riaFundGroupId.parent,
          toUpdateUser = toUpdateAdvisor,
          role = RiaEntityUserRole.EntityManager,
          actor = actor,
          shouldSendEmail = false
        )
      }

      _ <- FDBRecordDatabase.transact(RiaEntityFundAdvisorStoreOperations.Production)(
        _.upsert(
          key = RiaEntityFundAdvisorPrimaryKey(
            riaFundGroupId = riaFundGroupId,
            advisor = toUpdateAdvisor
          ),
          default = RiaEntityFundAdvisorModel(
            riaFundGroupId = riaFundGroupId,
            advisor = toUpdateAdvisor,
            role = role,
            invitedByFundOpt = invitedByFundOpt,
            createdAt = Option(now),
            createdBy = actor
          ),
          updateFn = _.copy(role = toBecomeRole)
        )
      )

      linkedFundSubRiaGroupId = riaFundGroupModel.fundSubRiaGroupId
      riaEntityRebacStore <- riaPermissionService.getRiaEntityRebacStore(riaFundGroupId.parent)
      fundSubRebacStore <- fundSubExternalIntegrationService.getFundSubRebacStore(linkedFundSubRiaGroupId.parent)

      // remove old ria fund group permission
      _ <- riaPermissionService.removeRiaFundGroupUserRoleRelationsR(
        riaFundGroupId = riaFundGroupId,
        userId = toUpdateAdvisor
      )(
        using riaEntityRebacStore
      )

      executiveAdmin <- executiveAdmin.userId

      _ <- FundSubRiaPermissionUtils.removeFundGroupRiaUserRoleRelationsR(
        fundSubRiaGroupId = linkedFundSubRiaGroupId,
        userId = toUpdateAdvisor,
        actor = actor.getOrElse(executiveAdmin)
      )(
        using fundSubRebacStore,
        teamService
      )

      // grant new ria fund group permission
      _ <- toBecomeRole match {
        case RiaEntityFundAdvisorRole.AdvisorAdmin =>
          for {
            _ <- riaPermissionService.createRiaFundGroupAdminRoleRelationsR(
              riaFundGroupId = riaFundGroupId,
              userId = toUpdateAdvisor
            )(
              using riaEntityRebacStore
            )
            _ <- FundSubRiaPermissionUtils.createFundGroupRiaAdminRoleRelationsR(
              fundSubRiaGroupId = linkedFundSubRiaGroupId,
              userId = toUpdateAdvisor,
              actor = actor.getOrElse(executiveAdmin)
            )(
              using fundSubRebacStore,
              teamService
            )
          } yield ()
        case RiaEntityFundAdvisorRole.AdvisorMember =>
          for {
            _ <- riaPermissionService.createRiaFundGroupMemberRoleRelationsR(
              riaFundGroupId = riaFundGroupId,
              userId = toUpdateAdvisor
            )(
              using riaEntityRebacStore
            )
            _ <- FundSubRiaPermissionUtils.createFundGroupRiaMemberRoleRelationsR(
              fundSubRiaGroupId = linkedFundSubRiaGroupId,
              userId = toUpdateAdvisor
            )(
              using fundSubRebacStore
            )
          } yield ()
        case _ => throw InvalidFundGroupRoleException
      }

      _ <- ZIO.when(shouldSendEmail) {
        riaEmailService.sendRiaFundGroupEmail(
          riaFundGroupId = riaFundGroupId,
          emailType = RiaFundGroupEmailType.RiaFundGroupUserRoleUpdated,
          receiver = toUpdateAdvisor,
          actor = actor
        )
      }
    } yield ()
  }

  def updateRiaFundGroupAdvisorRole(
    params: UpdateRiaFundGroupAdvisorRoleParams,
    actor: UserId,
    shouldSendEmail: Boolean = true
  ): Task[UpdateRiaFundGroupAdvisorRoleResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"$actor is updating RIA fund group advisor ${params.toUpdateAdvisor} role to ${params.toUpdateRole}"
      )
      actorRiaUserRole <- RiaEntityService.validateUserInRiaEntity(params.riaFundGroupId.parent, actor).map(_.role)
      actorRiaFundGroupRoleOpt <- FDBRecordDatabase
        .transact(RiaEntityFundAdvisorStoreOperations.Production)(
          _.getByRiaFundGroupAndAdvisor(params.riaFundGroupId, actor)
        )
        .map(_.map(_.role))

      _ <- ZIOUtils.validate(actorRiaUserRole.isEntityAdmin || actorRiaFundGroupRoleOpt.exists(_.isAdvisorAdmin)) {
        GeneralValidationException(
          s"$actor with user role $actorRiaUserRole and fund group role $actorRiaFundGroupRoleOpt" +
            s" does not have permission to update role of ${params.toUpdateAdvisor} to fund group role ${params.toUpdateRole}"
        )
      }

      _ <- updateRiaFundGroupAdvisorRoleUnsafe(
        riaFundGroupId = params.riaFundGroupId,
        toUpdateAdvisor = params.toUpdateAdvisor,
        role = params.toUpdateRole,
        actor = Option(actor),
        shouldSendEmail = shouldSendEmail,
        shouldDegradeRole = true
      )

    } yield UpdateRiaFundGroupAdvisorRoleResponse()
  }

  def establishFundLinkage(
    params: EstablishFundLinkageParams,
    actor: UserId
  ): Task[EstablishFundLinkageResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"Creating fund group for RIA entity ${params.riaEntityId} and fund ${params.fundSubRiaGroupToLink}"
      )

      riaRebacStoreOperation <- riaPermissionService.getRiaEntityRebacStore(params.riaEntityId)
      isRiaEntityMember <- riaPermissionService.checkIfUserIsEntityUserR(
        riaEntityId = params.riaEntityId,
        userId = actor
      )(
        using riaRebacStoreOperation
      )

      fundSubRebacStoreOperation <- fundSubExternalIntegrationService.getFundSubRebacStore(
        params.fundSubRiaGroupToLink.parent
      )

      hasPermissionToEstablishRiaEntityLinkage <- FundSubRiaPermissionUtils.checkPermissionToLinkRiaEntityR(
        fundSubRiaGroupId = params.fundSubRiaGroupToLink,
        userId = actor
      )(
        using fundSubRebacStoreOperation
      )

      _ <- ZIOUtils.validate(
        hasPermissionToEstablishRiaEntityLinkage &&
          isRiaEntityMember
      ) {
        GeneralServiceException(
          s"$actor does not have permission establish fund entity linkage"
        )
      }

      hasAlreadyLinkedToFund <- FDBRecordDatabase.transact(RiaFundGroupStoreOperations.Production)(
        _.getByRiaEntity(params.riaEntityId)
          .map(_.exists(_.fundSubRiaGroupId.parent == params.fundSubRiaGroupToLink.parent))
      )

      _ <- ZIOUtils.validate(!hasAlreadyLinkedToFund) {
        GeneralServiceException(
          s"${params.riaEntityId} is already linked to fund ${params.fundSubRiaGroupToLink.parent}"
        )
      }

      isRiaFundGroupLinked <- fundSubExternalIntegrationService
        .getLinkedRiaFundGroupIdForFundGroup(
          fundSubRiaGroupId = params.fundSubRiaGroupToLink,
          actor = actor
        )
        .map(_.nonEmpty)

      _ <- ZIOUtils.validate(!isRiaFundGroupLinked) {
        GeneralServiceException(
          s"${params.fundSubRiaGroupToLink.parent} is already linked to ria entity ${params.riaEntityId}"
        )
      }

      // create ria fund group
      now <- ZIO.succeed(Instant.now())
      riaFundGroupId <- ZIO.succeed(RiaFundGroupIdFactory.unsafeRandomId(params.riaEntityId))
      teamId <- teamService.createNewTeam(CreateNewTeamParams(riaFundGroupId))
      _ <- FDBRecordDatabase.transact(RiaFundGroupStoreOperations.Production)(
        _.create(
          RiaFundGroupModel(
            id = riaFundGroupId,
            fundSubRiaGroupId = params.fundSubRiaGroupToLink,
            teamId = teamId,
            createdBy = Option(actor),
            createdAt = Option(now)
          )
        )
      )

      // create ria entity ria fund group relation
      _ <- riaPermissionService.createRiaEntityFundGroupRelationsR(
        riaEntityId = params.riaEntityId,
        riaFundGroupId = riaFundGroupId
      )(
        using riaRebacStoreOperation
      )

      riaEntityEmailDomains <- FDBRecordDatabase.transact(RiaEntityEmailDomainRelationStoreOperations.Production)(
        _.getByRiaEntity(params.riaEntityId).map(_.map(_.emailDomain))
      )

      // establish fund entity linkage
      toTransferUsers <- fundSubExternalIntegrationService
        .establishRiaEntityLinkage(
          params = EstablishRiaEntityLinkageParams(
            fundSubRiaGroupId = params.fundSubRiaGroupToLink,
            riaFundGroupId = riaFundGroupId,
            riaEntityEmailDomains = riaEntityEmailDomains
          ),
          actor = actor
        )
        .map(_.toTransferAdvisors)

      riaEntityModel <- RiaEntityService.validateRiaEntityId(params.riaEntityId)

      toTransferUsersFundGroupRole <- ZIO.foreach(toTransferUsers) { toTransferUser =>
        for {
          riaEntityUserRelationOpt <- FDBRecordDatabase.transact(
            RiaEntityUserRelationStoreOperations.Production
          )(
            _.getByRiaEntityAndUser(
              riaEntityId = params.riaEntityId,
              userId = toTransferUser.userId
            )
          )

          fundGroupRole = riaEntityUserRelationOpt.fold(
            // if user does not exist in ria entity, then he should be member of fund group
            RiaEntityFundAdvisorRole.AdvisorMember
          ) { riaEntityUserRelation =>
            // if user is already admin or entity manager, then he should be admin of fund group
            if (
              riaEntityUserRelation.role == RiaEntityUserRole.EntityAdmin ||
              riaEntityUserRelation.role == RiaEntityUserRole.EntityManager
            ) {
              RiaEntityFundAdvisorRole.AdvisorAdmin
            } else {
              RiaEntityFundAdvisorRole.AdvisorMember
            }
          }

          _ <- inviteRiaFundAdvisorUnsafe(
            params = InviteRiaEntityFundAdvisorParams(
              riaFundGroupId = riaFundGroupId,
              inviteeUsersInfo = Seq(
                InviteRiaEntityFundAdvisorInfo(
                  inviteeUser = Right(toTransferUser.userId),
                  fundGroupRole = fundGroupRole
                )
              )
            ),
            invitedByFundOpt = Option(
              InvitedByFund(
                invitedBy = toTransferUser.invitedBy,
                invitedAt = toTransferUser.invitedAt
              )
            ),
            shouldSendEmail = false,
            isAllowReInvite = true,
            actor = Option(actor)
          )

          isJoinedEntity <- teamService.isJoined(riaEntityModel.teamId, toTransferUser.userId)
          _ <- ZIO.when(isJoinedEntity) {
            fundSubExternalIntegrationService.advisorJoinFund(
              fundSubRiaGroupId = params.fundSubRiaGroupToLink,
              entityName = riaEntityModel.name,
              actor = toTransferUser.userId
            )
          }

          _ <- convertToRiaOrderUnsafe(
            riaFundGroupId = riaFundGroupId,
            ordersAndSelectedMemberToValidateOrFundId = params.fundSubRiaGroupToLink.parent,
            actor = None
          )

        } yield fundGroupRole
      }

      hasFundGroupAdmin = toTransferUsersFundGroupRole.exists(_.isAdvisorAdmin)
      riaEntityAdmins <- riaEntityService.getRiaEntityAdminsUnsafe(params.riaEntityId)

      _ <- ZIO.foreach(riaEntityAdmins) { riaEntityAdmin =>
        for {
          _ <- riaEmailService.sendRiaFundGroupEmail(
            riaFundGroupId = riaFundGroupId,
            emailType = RiaFundGroupEmailType.RiaFundGroupLinked,
            receiver = riaEntityAdmin,
            actor = Option(actor)
          )
          _ <- ZIO.unless(hasFundGroupAdmin) {
            riaEmailService.sendRiaFundGroupEmail(
              riaFundGroupId = riaFundGroupId,
              emailType = RiaFundGroupEmailType.RiaFundGroupCreatedWithoutAdmin,
              receiver = riaEntityAdmin,
              actor = Option(actor)
            )
          }
        } yield ()

      }

    } yield EstablishFundLinkageResponse(
      riaFundGroupId = riaFundGroupId
    )
  }

  private def getAllAccessibleRiaEntitiesAsInfoToLink(
    actor: UserId,
    fundSubRiaGroupId: FundSubRiaGroupId
  ): Task[Seq[RiaEntityInfoToLink]] = {
    getAccessibleRiaEntity(
      params = GetAccessibleRiaEntitiesParams(),
      actor = actor
    ).map(_.riaEntities.map { riaEntity =>
      RiaEntityInfoToLink(
        id = riaEntity.id,
        name = riaEntity.name,
        emailDomains = riaEntity.emailDomains.map(_.emailDomain),
        numberOfLinkedFunds = riaEntity.linkedFunds.size,
        numberOfAdvisor = riaEntity.users.size,
        isAlreadyLinkedWithCurrentFund = riaEntity.linkedFunds
          .exists(_.fundSubRiaGroupId.parent == fundSubRiaGroupId.parent)
      )
    })
  }

  def handleHowUserJoinFromFundInvitation(
    params: HandleHowUserJoinFromFundInvitationParams,
    actor: UserId
  ): Task[HandleHowUserJoinFromFundInvitationResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is handling how user join from invitation")

      fundNameOpt <- fundSubExternalIntegrationService
        .getFundName(fundSubIds = List(params.fundSubRiaGroupId.parent))
        .map(_.get(params.fundSubRiaGroupId.parent))

      fundName <- ZIOUtils.optionToTask(
        fundNameOpt,
        GeneralServiceException(s"Cannot get fund name of ${params.fundSubRiaGroupId.parent}")
      )

      screen <- fundSubExternalIntegrationService
        .getLinkedRiaFundGroupIdForFundGroup(
          fundSubRiaGroupId = params.fundSubRiaGroupId,
          actor = actor
        )
        .flatMap { linkedRiaFundGroupIdOpt =>
          for {
            given RebacStoreOperation <- fundSubExternalIntegrationService.getFundSubRebacStore(
              params.fundSubRiaGroupId.parent
            )
            hasPermissionToEstablishRiaEntityLinkage <- FundSubRiaPermissionUtils.checkPermissionToLinkRiaEntityR(
              fundSubRiaGroupId = params.fundSubRiaGroupId,
              userId = actor
            )

            screen: SetupRiaEntityFlow <- linkedRiaFundGroupIdOpt.fold(
              // if this group has no linked entity
              if (hasPermissionToEstablishRiaEntityLinkage) {
                for {
                  // Get actor's email domain
                  actorEmail <- userProfileService.getEmailAddress(actor)
                  actorEmailDomain = actorEmail.domain.value

                  // Get RIA entity's email domains
                  riaEntityActorShouldBelongToOpt <- FDBRecordDatabase
                    .transact(RiaEntityEmailDomainRelationStoreOperations.Production)(
                      _.getByEmailDomain(
                        emailDomain = actorEmailDomain
                      )
                    )

                  screen <- riaEntityActorShouldBelongToOpt.fold(
                    for {
                      riaEntityRelationsOfCurrentUser <- FDBRecordDatabase.transact(
                        RiaEntityUserRelationStoreOperations.Production
                      )(_.getByUser(actor))

                      screen <- riaEntityRelationsOfCurrentUser match {
                        // if user is not in any ria entity, then show setup entity screen
                        case Nil => ZIO.succeed(SetupRiaEntityFlow.CreateEntity)
                        // if user exist in many ria entities, then show select entity screen
                        case _ =>
                          for {
                            riaEntities <- getAccessibleRiaEntity(
                              params = GetAccessibleRiaEntitiesParams(),
                              actor = actor
                            ).map(_.riaEntities)
                            riaEntitiesInfoToLink = riaEntities.map { riaEntity =>
                              RiaEntityInfoToLink(
                                id = riaEntity.id,
                                name = riaEntity.name,
                                emailDomains = riaEntity.emailDomains.map(_.emailDomain),
                                numberOfLinkedFunds = riaEntity.linkedFunds.size,
                                numberOfAdvisor = riaEntity.advisors.size,
                                isAlreadyLinkedWithCurrentFund = riaEntity.linkedFunds
                                  .exists(_.fundSubRiaGroupId.parent == params.fundSubRiaGroupId.parent)
                              )
                            }
                          } yield SetupRiaEntityFlow.SelectOrCreateEntity(riaEntitiesInfoToLink)
                      }
                    } yield screen
                  ) { riaEntityActorShouldBelongTo =>
                    for {
                      riaEntityModel <- RiaEntityService.validateRiaEntityId(riaEntityActorShouldBelongTo.riaEntityId)
                      entitiesToSelect <- getAllAccessibleRiaEntitiesAsInfoToLink(
                        actor = actor,
                        fundSubRiaGroupId = params.fundSubRiaGroupId
                      )
                      screen <-
                        if (entitiesToSelect.exists(_.id == riaEntityActorShouldBelongTo.riaEntityId)) {
                          ZIO.succeed(SetupRiaEntityFlow.SelectOrCreateEntity(entitiesToSelect))
                        } else {
                          if (riaEntityModel.enableAutoAddWithEmailDomain) {
                            for {
                              // Auto-add user using inviteRiaFundAdvisor
                              _ <- inviteRiaUserUnsafe(
                                params = InviteRiaUserParams(
                                  riaEntityId = riaEntityActorShouldBelongTo.riaEntityId,
                                  riaUserDetails = Seq(
                                    InviteRiaUserDetail(
                                      user = Right(actor),
                                      role = RiaEntityUserRole.EntityMember
                                    )
                                  )
                                ),
                                actor = None
                              )
                              entitiesToSelect <- getAllAccessibleRiaEntitiesAsInfoToLink(
                                actor = actor,
                                fundSubRiaGroupId = params.fundSubRiaGroupId
                              )
                            } yield SetupRiaEntityFlow.SelectOrCreateEntity(
                              entitiesToSelect = entitiesToSelect
                            )
                          } else {
                            for {
                              riaEntityAdmins <- riaEntityService.getRiaEntityAdminsUnsafe(
                                riaEntityId = riaEntityActorShouldBelongTo.riaEntityId
                              )
                              adminInfos <- userProfileService.batchGetUserInfos(riaEntityAdmins.toSet)
                              adminEmails = adminInfos.values.map(_.emailAddressStr).toSeq
                            } yield SetupRiaEntityFlow.SuggestContactEntityAdmin(
                              suggestEntityName = riaEntityModel.name,
                              suggestEntityAdminEmails = adminEmails,
                              entitiesToSelect = entitiesToSelect
                            )
                          }
                        }

                    } yield screen
                  }
                } yield screen
              } else {
                ZIO.succeed(SetupRiaEntityFlow.Invalid)
              }
            ) { linkedRiaFundGroupId =>
              // if this group has linked entity
              for {
                _ <- ZIO.when(hasPermissionToEstablishRiaEntityLinkage) {
                  // this fund group group has linked entity, but this user still has permission
                  // to establish fund linkage. This should never happen as we revoked all permission
                  // to establish fund linkage to that fund group right after the link is established
                  ZIO.logWarning(
                    "User still has permission to establish fund linkage for this fund group despite this group already has linked entity"
                  )
                }

                relationOfLinkedRiaEntityWithCurrentUserOpt <- FDBRecordDatabase.transact(
                  RiaEntityUserRelationStoreOperations.Production
                )(
                  _.getByRiaEntityAndUser(
                    riaEntityId = linkedRiaFundGroupId.parent,
                    userId = actor
                  )
                )

                screen <- relationOfLinkedRiaEntityWithCurrentUserOpt.fold[Task[SetupRiaEntityFlow]](
                  // if user does not belong to linked ria entity, then show invalid screen
                  ZIO.succeed(SetupRiaEntityFlow.Invalid)
                ) { _ =>
                  // if user belong to linked ria entity
                  for {
                    isCurrentUserAlreadyInFundGroup <- FDBRecordDatabase
                      .transact(RiaEntityFundAdvisorStoreOperations.Production)(
                        _.getByRiaFundGroupAndAdvisor(
                          riaFundGroupId = linkedRiaFundGroupId,
                          advisor = actor
                        )
                      )
                      .map(_.nonEmpty)

                    screen =
                      if (isCurrentUserAlreadyInFundGroup) {
                        SetupRiaEntityFlow.SkipToFundGroupScreen(linkedRiaFundGroupId)
                      } else {
                        SetupRiaEntityFlow.Invalid
                      }
                  } yield screen
                }
              } yield screen
            }
          } yield screen
        }
        .catchSome { case _: NotHavePermissionToGetLinkedRiaGroupException =>
          ZIO.succeed(SetupRiaEntityFlow.Invalid)
        }
    } yield HandleHowUserJoinFromFundInvitationResponse(
      screen = screen,
      fundName = fundName
    )
  }

  def createSubscriptions(
    params: CreateSubscriptionParams,
    actor: UserId,
    httpContextOpt: Option[AuthenticatedRequestContext] = None
  ): Task[CreateSubscriptionResponse] = {
    for {
      given RebacStoreOperation <- riaPermissionService.getRiaEntityRebacStore(params.riaFundGroupId.parent)
      _ <- ZIO.foreach(params.subscriptions) { subscription =>
        riaPermissionService.validateUserCanCreateOrderInFundGroupR(
          riaFundGroupId = params.riaFundGroupId,
          advisorUserId = subscription.advisorUserId,
          userId = actor
        )
      }
      ordersToAdd = params.subscriptions.map { subscription =>
        AdvisorCreateOrderInfo(
          investmentEntityName = subscription.investmentEntityName,
          advisorUserId = subscription.advisorUserId
        )
      }
      linkedFundSubRiaGroupId <- FDBRecordDatabase.transact(RiaFundGroupStoreOperations.Production)(
        _.getByFundSubRiaGroup(params.riaFundGroupId).map(_.fundSubRiaGroupId)
      )
      res <- fundSubExternalIntegrationService.advisorCreateSubscriptions(
        ordersToAdd = ordersToAdd,
        advisorGroupId = linkedFundSubRiaGroupId,
        actor = actor,
        httpContextOpt = httpContextOpt
      )
    } yield CreateSubscriptionResponse(
      lpIds = res.lpIds,
      failedMessages = res.failedLps.map(_._2.toString)
    )
  }

  def createSubscriptionsWorkflow(
    params: CreateSubscriptionParams,
    actor: UserId,
    httpContextOpt: Option[AuthenticatedRequestContext] = None
  ): Task[EmptyResponse] = {
    for {
      given RebacStoreOperation <- riaPermissionService.getRiaEntityRebacStore(params.riaFundGroupId.parent)
      _ <- ZIO.foreach(params.subscriptions) { subscription =>
        riaPermissionService.validateUserCanCreateOrderInFundGroupR(
          riaFundGroupId = params.riaFundGroupId,
          advisorUserId = subscription.advisorUserId,
          userId = actor
        )
      }

      ordersToAdd = params.subscriptions.map { subscription =>
        AdvisorCreateOrderInfo(
          investmentEntityName = subscription.investmentEntityName,
          advisorUserId = subscription.advisorUserId
        )
      }
      linkedFundSubRiaGroupId <- FDBRecordDatabase.transact(RiaFundGroupStoreOperations.Production)(
        _.getByFundSubRiaGroup(params.riaFundGroupId).map(_.fundSubRiaGroupId)
      )
      _ <- fundSubExternalIntegrationService.advisorCreateSubscriptionsWorkflow(
        ordersToAdd = ordersToAdd,
        advisorGroupId = linkedFundSubRiaGroupId,
        actor = actor,
        httpContextOpt = httpContextOpt
      )
    } yield EmptyResponse()
  }

  // Use when user landing on ria app
  def joinRiaEntity(
    params: JoinRiaEntityParams,
    actor: UserId
  ): Task[JoinRiaEntityResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is joining RIA entity ${params.riaEntityId}")
      riaEntityModel <- RiaEntityService.validateRiaEntityId(params.riaEntityId)

      given RebacStoreOperation <- riaPermissionService.getRiaEntityRebacStore(params.riaEntityId)
      _ <- riaPermissionService.validateUserIsEntityUserR(
        riaEntityId = params.riaEntityId,
        userId = actor
      )

      isJoined <- teamService.isJoined(riaEntityModel.teamId, actor)
      _ <- ZIO.when(!isJoined) {
        for {
          advisorFundRelations <- FDBRecordDatabase
            .transact(RiaEntityFundAdvisorStoreOperations.Production)(
              _.getByRiaEntityAndAdvisor(
                riaEntityId = params.riaEntityId,
                advisor = actor
              )
            )

          _ <- ZIO.foreach(advisorFundRelations) { advisorFundRelation =>
            ZIO.when(advisorFundRelation.invitedByFundOpt.nonEmpty) {
              for {
                linkedFundSubRiaGroupIdOpt <- FDBRecordDatabase
                  .transact(RiaFundGroupStoreOperations.Production)(
                    _.getByRiaFundGroup(advisorFundRelation.riaFundGroupId)
                  )
                  .map(_.map(_.fundSubRiaGroupId))
                _ <- ZIOUtils.traverseOption(linkedFundSubRiaGroupIdOpt) { linkedFundSubRiaGroupId =>
                  fundSubExternalIntegrationService.advisorJoinFund(
                    fundSubRiaGroupId = linkedFundSubRiaGroupId,
                    entityName = riaEntityModel.name,
                    actor = actor
                  )
                }
              } yield ()
            }
          }

          _ <- teamService.acceptInvitation(
            AcceptInviteParams(
              invitee = actor,
              teamId = riaEntityModel.teamId
            )
          )

        } yield ()

      }
    } yield JoinRiaEntityResponse()
  }

  def convertToRiaOrder(
    params: ConvertToRiaOrderParams,
    actor: UserId
  ): Task[ConvertToRiaOrderResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is converting order ${params.toConvertOrders} to RIA order")

      given RebacStoreOperation <- riaPermissionService.getRiaEntityRebacStore(params.riaFundGroupId.parent)
      _ <- riaPermissionService.validateUserCanCreateOrderInFundGroupR(
        riaFundGroupId = params.riaFundGroupId,
        userId = actor
      )
      _ <- RiaEntityService.validateRiaFundGroupId(params.riaFundGroupId)
      _ <- RiaEntityService.validateRiaEntityId(params.riaFundGroupId.parent)
      actorFundGroupRole <- RiaEntityService.validateUserInRiaFundGroup(params.riaFundGroupId, actor).map(_.role)

      _ <- FDBRecordDatabase.transact(RiaEntityOrderRelationStoreOperations.Production) { ops =>
        RecordIO.traverse(params.toConvertOrders) { orderId =>
          for {
            isAlreadyBelongToAnEntity <- ops.getByOrder(orderId).map(_.isDefined)
            _ <- RecordIO.validate(!isAlreadyBelongToAnEntity) {
              GeneralValidationException(s"Order $orderId already belong to an entity")
            }
          } yield ()
        }
      }

      toConvertOrdersInfo <- greylinDataService
        .run(
          SubscriptionOrderMemberOperations.getBySubscriptionOrderIds(params.toConvertOrders.toList)
        )
        .map(_.groupMap(_.subscriptionOrderId)(_.userId))

      fundGroupMembers <- FDBRecordDatabase
        .transact(RiaEntityFundAdvisorStoreOperations.Production)(
          _.getByRiaFundGroup(
            riaFundGroupId = params.riaFundGroupId
          )
        )
        .map(_.map(_.advisor))

      // validate permission to convert order
      _ <- ZIO.foreach(toConvertOrdersInfo.toSeq) { case (orderId, orderMembers) =>
        for {
          canAccessOrder <- fundSubExternalIntegrationService.checkIfUserCanManageInvestorOrServeOnInvestorSide(
            lpId = orderId,
            actor = actor
          )
          _ <- ZIOUtils.validate(canAccessOrder) {
            GeneralValidationException(
              s"$actor doesn't has permission to convert order $orderId"
            )
          }

          _ <- actorFundGroupRole match {
            case RiaEntityFundAdvisorRole.AdvisorMember =>
              ZIOUtils.validate(orderMembers.contains(actor)) {
                GeneralValidationException(s"You don't have permission to convert order $orderId to RIA order")
              }
            case RiaEntityFundAdvisorRole.AdvisorAdmin =>
              ZIOUtils.validate(
                orderMembers.contains(actor) || fundGroupMembers.intersect(orderMembers).nonEmpty
              )(
                GeneralValidationException(s"You don't have permission to convert order $orderId to RIA order")
              )
            case RiaEntityFundAdvisorRole.Unrecognized(_) =>
              ZIO.fail(GeneralValidationException("Unknown fund group role"))
          }
        } yield ()
      }

      _ <- convertToRiaOrderUnsafe(
        riaFundGroupId = params.riaFundGroupId,
        ordersAndSelectedMemberToValidateOrFundId = toConvertOrdersInfo.keys.toSeq.map(_ -> None).toMap,
        actor = Option(actor)
      )

    } yield ConvertToRiaOrderResponse()
  }

  private def getValidOrderMembersToBeFundGroupMember(
    riaFundGroupId: RiaFundGroupId,
    ordersAndSelectedMemberToValidate: Map[FundSubLpId, Option[UserId]]
  ): Task[Map[FundSubLpId, Seq[UserId]]] = {
    for {
      riaFundGroupModel <- RiaEntityService.validateRiaFundGroupId(riaFundGroupId)
      riaEntityModel <- RiaEntityService.validateRiaEntityId(riaFundGroupModel.id.parent)
      riaEntityEmailDomains <- FDBRecordDatabase
        .transact(
          RiaEntityEmailDomainRelationStoreOperations.Production
        ) { ops =>
          ops.getByRiaEntity(riaFundGroupId.parent)
        }
        .map(_.map(_.emailDomain).toSet)
      riaFundGroupMembers <- FDBRecordDatabase
        .transact(
          RiaEntityFundAdvisorStoreOperations.Production
        )(_.getByRiaFundGroup(riaFundGroupId))
        .map(_.map(_.advisor).toSet)

      orderMembersMap <-
        greylinDataService
          .run(SubscriptionOrderMemberOperations.getBySubscriptionOrderIds(ordersAndSelectedMemberToValidate.keys.toList))
          .map(_.groupMap(_.subscriptionOrderId)(_.userId).map { case (orderId, orderMembers) =>
            orderId -> ordersAndSelectedMemberToValidate
              .get(orderId)
              .flatten
              .fold(orderMembers) { seletedOrderMember =>
                orderMembers.filter(_ == seletedOrderMember)
              }
          })

      userInfoMap <- userProfileService.batchGetUserInfos(orderMembersMap.values.flatten.toSet)

      refinedOrderMemberMapWithMatchedEmailDomain = orderMembersMap.map { (orderId, orderMembers) =>
        val orderMembersInfo = orderMembers.flatMap { orderMemberUserId =>
          userInfoMap.get(orderMemberUserId).map((orderMemberUserId, _))
        }
        val refinedOrderMembersByEmailDomain = orderMembersInfo
          .filter { case (userId, userInfo) =>
            userInfo.emailAddress.exists { email =>
              riaFundGroupMembers.contains(userId) ||
              (riaEntityModel.enableAutoAddWithEmailDomain && riaEntityEmailDomains.contains(email.domain.value))
            }
          }
          .map(_._1)
        orderId -> refinedOrderMembersByEmailDomain
      }
    } yield refinedOrderMemberMapWithMatchedEmailDomain
  }

  /** This will convert valid orders to RIA orders and invite all matched email domain order's members to fund group. A
    * valid order is and order that has at least: a matched email domain member or a fund group member
    *
    * @param riaFundGroupId
    * @param ordersAndSelectedMemberToValidateOrFundId
    *   - If is FundSubId, find all valid orders of that fund and do the conversion
    *   - If is Map[FundSubLpId, Option[UserId]], only consider orders in the map:
    *     - If Option[UserId] is defined, only consider that order member user to decide if the order is valid, and if
    *       this order is consider valid the conversion will only invite this user to fund group.
    *     - If Option[UserId] is not defined, consider all order members' email domain
    * @param actor
    * @return
    */
  private def convertToRiaOrderUnsafe(
    riaFundGroupId: RiaFundGroupId,
    ordersAndSelectedMemberToValidateOrFundId: Map[FundSubLpId, Option[UserId]] | FundSubId,
    actor: Option[UserId]
  ): Task[Unit] = {
    for {
      riaFundGroupModel <- RiaEntityService.validateRiaFundGroupId(riaFundGroupId)
      riaEntityModel <- RiaEntityService.validateRiaEntityId(riaFundGroupModel.id.parent)

      ordersAndSelectedMemberToValidate <- ordersAndSelectedMemberToValidateOrFundId match {
        case orderIds: Map[FundSubLpId, Option[UserId]] =>
          ZIO.succeed(orderIds)
        case fundSubId: FundSubId =>
          greylinDataService
            .run(SubscriptionOrderMemberOperations.get(fundSubId))
            .map(_.map(_.subscriptionOrderId -> None).toMap)
      }

      refinedOrderMemberMap <- getValidOrderMembersToBeFundGroupMember(
        riaFundGroupId = riaFundGroupId,
        ordersAndSelectedMemberToValidate = ordersAndSelectedMemberToValidate
      ).map(_.filter(_._2.nonEmpty)) // only keep orders that contain at least a matched email domain member

      _ <- ZIOUtils.foreachParN(4)(refinedOrderMemberMap.toSeq) { case (orderId, advisors) =>
        for {
          _ <- inviteRiaFundAdvisorUnsafe(
            params = InviteRiaEntityFundAdvisorParams(
              riaFundGroupId = riaFundGroupId,
              inviteeUsersInfo = advisors.map { advisorUserId =>
                InviteRiaEntityFundAdvisorInfo(
                  inviteeUser = Right(advisorUserId),
                  fundGroupRole = RiaEntityFundAdvisorRole.AdvisorMember
                )
              }
            ),
            actor = actor,
            isAllowReInvite = true
          )
          _ <- fundSubExternalIntegrationService.convertToRiaOrderUnsafe(
            orderToConvert = orderId,
            fundSubRiaGroupId = riaFundGroupModel.fundSubRiaGroupId,
            riaEntityName = riaEntityModel.name,
            shouldHasAuditLog = true,
            actor = actor
          )
          _ <- riaEntityService.createRiaOrderRelationsUnsafe(
            orderId = orderId,
            riaFundGroupId = riaFundGroupId,
            advisorUserIds = advisors
          )
        } yield ()
      }
    } yield ()
  }

  /** @param orderId
    *   order to convert
    * @param orderMemberToValidateEmailDomain
    *   if this is defined, only consider this user's email domain to convert order. If this is not defined, consider
    *   all order members' email domain
    * @return
    */
  private def convertOrderToFundGroupShouldBelongTo(
    orderId: FundSubLpId,
    orderMemberToValidateEmailDomain: Option[UserId]
  ): Task[Unit] = {
    for {
      linkedFundGroupOrderWillBelongToOpt <- getFundGroupThatOrderShouldBelongTo(
        orderId = orderId,
        orderMemberToValidateEmailDomain = orderMemberToValidateEmailDomain
      )

      _ <- ZIOUtils.traverseOption(linkedFundGroupOrderWillBelongToOpt) { linkedFundGroupOrderWillBelongTo =>
        for {
          _ <- RiaEntityService.validateRiaEntityId(
            linkedFundGroupOrderWillBelongTo.id.parent
          )
          _ <- convertToRiaOrderUnsafe(
            riaFundGroupId = linkedFundGroupOrderWillBelongTo.id,
            ordersAndSelectedMemberToValidateOrFundId = Map(orderId -> orderMemberToValidateEmailDomain),
            actor = None
          )
        } yield ()
      }
    } yield ()
  }

  private def getFundGroupThatUsersShouldBelongTo(
    users: Seq[(UserId, UserInfo)],
    riaFundGroupUserBelongToMap: Map[UserId, Seq[RiaFundGroupModel]],
    fundGroupEmailDomainMap: Map[String, RiaFundGroupModel]
  ): Option[RiaFundGroupModel] = {
    users.flatMap { case (userId, _) =>
      riaFundGroupUserBelongToMap.get(userId).flatMap {
        case fundGroup :: Nil => Option(fundGroup)
        case _                => None
      }
    }.distinct match {
      case fundGroup :: Nil => Option(fundGroup)
      case _ =>
        users.flatMap { case (_, userInfo) =>
          userInfo.emailAddress.map(_.domain.value).flatMap(fundGroupEmailDomainMap.get)
        }.distinct match {
          case fundGroup :: Nil => Option(fundGroup)
          case _                => None
        }
    }
  }

  /** @param orderId
    *   order to consider
    * @param orderMemberToValidateEmailDomain
    *   Specify an order's member to consider:
    *   - If this is defined, only consider this user's email domain
    *   - If this is not defined, first, consider email domain of main lp user then all other collaborators sequentially
    *     - If there are multiple matched email domains across collaborators, return nothing
    * @return
    */
  private def getFundGroupThatOrderShouldBelongTo(
    orderId: FundSubLpId,
    orderMemberToValidateEmailDomain: Option[UserId]
  ): Task[Option[RiaFundGroupModel]] = {
    for {
      orderMembers <- greylinDataService
        .run(SubscriptionOrderMemberOperations.get(orderId))
        .map(_.map(_.userId))

      (emailDomainWithLinkedFundGroupMap, riaFundGroupUserBelongToMap) <- FDBRecordDatabase.transact(
        FDBOperations[
          (
            RiaFundGroupStoreOperations,
            RiaEntityEmailDomainRelationStoreOperations,
            RiaEntityFundAdvisorStoreOperations
          )
        ].Production
      ) { case (riaFundGroupOps, riaEntityEmailDomainOps, riaEntityFundAdvisorOps) =>
        for {
          linkedFundGroups <- riaFundGroupOps.getByFund(orderId.parent)
          emailDomainWithLinkedFundGroupMap <- RecordIO
            .traverse(linkedFundGroups) { linkedFundGroup =>
              riaEntityEmailDomainOps
                .getByRiaEntity(linkedFundGroup.id.parent)
                .map(_.map { relation =>
                  relation.emailDomain -> linkedFundGroup
                })
            }
            .map(_.flatten.toMap)

          riaFundGroupUserBelongToMap <- RecordIO
            .parTraverseN(4)(orderMembers) { orderMember =>
              for {
                riaFundGroupUserBelongTo <- riaEntityFundAdvisorOps.getByAdvisor(orderMember)
                riaFundGroupUserBelongToInFund = linkedFundGroups.filter { linkedFundGroup =>
                  riaFundGroupUserBelongTo.exists(_.riaFundGroupId == linkedFundGroup.id)
                }
              } yield orderMember -> riaFundGroupUserBelongToInFund
            }
            .map(_.toMap)

        } yield (emailDomainWithLinkedFundGroupMap, riaFundGroupUserBelongToMap)
      }

      linkedFundGroupOrderWillBelongToOpt <- orderMemberToValidateEmailDomain.fold(
        for {
          mainLpUserId <- greylinDataService.run(SubscriptionOrderOperations.get(orderId)).map(_.mainUserId)
          collaboratorIds = orderMembers.filterNot(_ == mainLpUserId)

          mainLpInfo <- userProfileService.getUserInfo(mainLpUserId)

          investorCollaboratorInfos <- userProfileService
            .batchGetUserInfos(collaboratorIds.toSet)
            .map(_.toSeq)

          linkedFundGroupOrderWillBelongToOpt = getFundGroupThatUsersShouldBelongTo(
            users = Seq((mainLpUserId, mainLpInfo)),
            riaFundGroupUserBelongToMap = riaFundGroupUserBelongToMap,
            fundGroupEmailDomainMap = emailDomainWithLinkedFundGroupMap
          ).orElse(
            getFundGroupThatUsersShouldBelongTo(
              users = investorCollaboratorInfos,
              riaFundGroupUserBelongToMap = riaFundGroupUserBelongToMap,
              fundGroupEmailDomainMap = emailDomainWithLinkedFundGroupMap
            )
          )

        } yield linkedFundGroupOrderWillBelongToOpt
      ) { orderMemberToValidateEmailDomain =>
        val orderMemberOpt = orderMembers.find(_ == orderMemberToValidateEmailDomain)
        orderMemberOpt.fold(
          for {
            _ <- ZIO.logWarning(s"Order $orderId doesn't have order member $orderMemberToValidateEmailDomain")
          } yield Option.empty[RiaFundGroupModel]
        ) { orderMember =>
          for {
            orderMemberInfo <- userProfileService.getUserInfo(orderMember)

            linkedFundGroupOrderWillBelongToOpt = getFundGroupThatUsersShouldBelongTo(
              users = Seq((orderMember, orderMemberInfo)),
              riaFundGroupUserBelongToMap = riaFundGroupUserBelongToMap,
              fundGroupEmailDomainMap = emailDomainWithLinkedFundGroupMap
            )

          } yield linkedFundGroupOrderWillBelongToOpt
        }
      }
    } yield linkedFundGroupOrderWillBelongToOpt
  }

  def hideConvertToRiaOrderBanner(
    params: HideConvertToRiaOrderBannerParams,
    actor: UserId
  ): Task[HideConvertToRiaOrderBannerResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is hiding convert to RIA order banner")
      _ <- RiaEntityService.validateUserInRiaFundGroup(params.riaFundGroupId, actor)

      convertibleOrderIds <- getConvertibleOrders(
        params = GetConvertibleOrderParams(
          riaFundGroupId = params.riaFundGroupId
        ),
        actor = actor
      ).map(_.orderIds)

      _ <- FDBRecordDatabase
        .transact(RiaEntityFundAdvisorStoreOperations.Production)(
          _.update(
            key = RiaEntityFundAdvisorPrimaryKey(
              riaFundGroupId = params.riaFundGroupId,
              advisor = actor
            ),
            updateFn = _.copy(
              hideConvertOrderBanner = convertibleOrderIds
            )
          )
        )

    } yield HideConvertToRiaOrderBannerResponse()
  }

  def getAccessibleNonRiaOrders(
    riaFundGroupId: RiaFundGroupId,
    actor: UserId
  ): Task[Seq[FundSubLpId]] = {
    for {
      riaFundGroupModel <- RiaEntityService.validateRiaFundGroupId(riaFundGroupId)

      accessibleOrderIds <- fundSubExternalIntegrationService.getAccessibleLpIds(
        fundSubId = riaFundGroupModel.fundSubRiaGroupId.parent,
        actor = actor
      )

      fundGroupRiaOrderIds <- FDBRecordDatabase.transact(RiaEntityOrderRelationStoreOperations.Production)(
        _.getByFund(riaFundGroupModel.fundSubRiaGroupId.parent).map(_.map(_.orderId))
      )

      accessibleNonRiaOrderIds = accessibleOrderIds.toSeq.diff(fundGroupRiaOrderIds)

      // filter out removed and offline order
      refinedAccessibleNonRiaOrderIds <- fundSubExternalIntegrationService
        .getFundSubLpModels(accessibleNonRiaOrderIds.toList, actor)
        .map(_.values.toSeq.filterNot { lpModel =>
          lpModel.orderType.isOfflineOrder || lpModel.lpState.exists(_.isRemoved)
        })
        .map(_.map(_.fundSubLpId))

    } yield refinedAccessibleNonRiaOrderIds
  }

  // only keep orders that contain at least a fund group member
  def filterOrdersForFundGroupAdmin(
    riaFundGroupId: RiaFundGroupId,
    orders: Seq[(FundSubLpId, Seq[UserId])] // order members
  ): Task[Seq[FundSubLpId]] = {
    for {
      fundGroupMembers <- FDBRecordDatabase
        .transact(RiaEntityFundAdvisorStoreOperations.Production)(
          _.getByRiaFundGroup(
            riaFundGroupId = riaFundGroupId
          )
        )
        .map(_.map(_.advisor))

      refinedOrders = orders
        .filter { case (_, orderMembers) =>
          fundGroupMembers.intersect(orderMembers).nonEmpty
        }
        .map(_._1)
    } yield refinedOrders
  }

  def getConvertibleOrders(
    params: GetConvertibleOrderParams,
    actor: UserId
  ): Task[GetConvertibleOrderResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is getting convertible order of ${params.riaFundGroupId}")

      advisorModelOpt <- RiaEntityService
        .validateUserInRiaFundGroup(params.riaFundGroupId, actor)
        .map(Option(_))
        .catchSome { case _: UserNotInRiaFundGroupException =>
          ZIO.succeed(None)
        }

      res <- advisorModelOpt.fold(
        ZIO.succeed(
          GetConvertibleOrderResponse(
            orderIds = Seq.empty,
            shouldShowBanner = false
          )
        )
      ) { advisorModel =>
        for {
          accessibleNonRiaOrderIds <- getAccessibleNonRiaOrders(params.riaFundGroupId, actor)
          orderIds <- advisorModel.role match {
            case RiaEntityFundAdvisorRole.AdvisorMember => ZIO.succeed(accessibleNonRiaOrderIds)
            case RiaEntityFundAdvisorRole.AdvisorAdmin =>
              for {
                orderMembers <- greylinDataService.run(
                  SubscriptionOrderMemberOperations
                    .getBySubscriptionOrderIds(accessibleNonRiaOrderIds.toList)
                    .map(_.groupMap(_.subscriptionOrderId)(_.userId).toSeq)
                )
                refinedOrderIds <- filterOrdersForFundGroupAdmin(params.riaFundGroupId, orderMembers)
              } yield refinedOrderIds
            case RiaEntityFundAdvisorRole.Unrecognized(_) => ZIO.succeed(Seq())
          }
        } yield GetConvertibleOrderResponse(
          orderIds = orderIds,
          // if there is no change since last hide convert to RIA order banner should not show banner
          shouldShowBanner = orderIds.diff(advisorModel.hideConvertOrderBanner).nonEmpty
        )
      }

    } yield res
  }

  private def unlinkOrderUnsafe(
    riaFundGroupId: RiaFundGroupId,
    orderId: FundSubLpId
  ): Task[Unit] = {
    for {
      riaFundGroupModel <- RiaEntityService.validateRiaFundGroupId(riaFundGroupId)
      _ <- riaEntityService.removeRiaOrderRelationsUnsafe(
        riaEntityId = riaFundGroupId.parent,
        orderId = orderId
      )
      _ <- fundSubExternalIntegrationService.unlinkAdvisorOrderUnsafe(
        fundSubRiaGroupId = riaFundGroupModel.fundSubRiaGroupId,
        lpId = orderId
      )
    } yield ()
  }

  def unlinkRiaOrder(
    params: UnlinkRiaOrderParams,
    actor: UserId
  ): Task[UnlinkRiaOrderResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is unlinking RIA order ${params.orderId} from ${params.riaFundGroupId}")
      riaEntityModel <- RiaEntityService.validateRiaEntityId(params.riaFundGroupId.parent)
      riaFundGroupModel <- RiaEntityService.validateRiaFundGroupId(params.riaFundGroupId)

      given RebacStoreOperation <- riaPermissionService.getRiaEntityRebacStore(params.riaFundGroupId.parent)
      _ <- riaPermissionService
        .validateUserIsOrderAdvisorR(
          orderId = params.orderId,
          userId = actor
        )
        .orElse(
          riaPermissionService.validateUserHasFundGroupAdminRoleR(
            riaFundGroupId = params.riaFundGroupId,
            userId = actor
          )
        )

      _ <- riaEntityService.removeRiaOrderRelationsUnsafe(
        riaEntityId = params.riaFundGroupId.parent,
        orderId = params.orderId
      )
      _ <- fundSubExternalIntegrationService.unlinkAdvisorOrder(
        fundSubRiaGroupId = riaFundGroupModel.fundSubRiaGroupId,
        riaEntityName = riaEntityModel.name,
        lpId = params.orderId,
        actor = actor
      )
    } yield UnlinkRiaOrderResponse()
  }

}
