// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.module

import com.softwaremill.macwire.wire

import anduin.fundsub.integration.FundSubExternalIntegrationService
import anduin.ria.appnavigation.RiaAppNavigatorService
import anduin.ria.dashboard.RiaDashboardService
import anduin.ria.email.RiaEmailService
import anduin.ria.entity.fund.RiaFundGroupService
import anduin.ria.entity.{RiaEntityFundIntegrationService, RiaEntityService}
import anduin.ria.event.RiaEventService
import anduin.ria.integration.{RiaExternalIntegrationService, RiaExternalIntegrationServiceImpl}
import anduin.ria.permission.RiaPermissionService

trait RiaServiceModule extends GondorCoreServiceModule with GreylinCoreServiceModule {

  def fundSubExternalIntegrationService: FundSubExternalIntegrationService

  // Keep at least 1 unwired service to combat zinc bug
  given riaExternalIntegrationService: RiaExternalIntegrationService = RiaExternalIntegrationServiceImpl(
    riaEntityService,
    riaPermissionService
  )

  given riaEntityFundIntegrationService: RiaEntityFundIntegrationService = wire[RiaEntityFundIntegrationService]

  given riaEntityService: RiaEntityService = wire[RiaEntityService]

  given riaPermissionService: RiaPermissionService = wire[RiaPermissionService]

  given riaEmailService: RiaEmailService = wire[RiaEmailService]

  given riaEventService: RiaEventService = wire[RiaEventService]

  given riaDashboardService: RiaDashboardService = wire[RiaDashboardService]

  given riaFundGroupService: RiaFundGroupService = wire[RiaFundGroupService]

  given riaAppNavigatorService: RiaAppNavigatorService = wire[RiaAppNavigatorService]
}
