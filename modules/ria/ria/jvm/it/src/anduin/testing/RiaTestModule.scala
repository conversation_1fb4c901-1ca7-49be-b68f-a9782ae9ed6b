// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.testing

import anduin.dataextract.integration.DataExtractIntegrationService
import anduin.dataroom.bot.DataRoomBotService
import anduin.dataroom.integration.service.{DataRoomExternalIntegrationService, DataRoomInternalIntegrationService}
import anduin.environment.{
  DataRoomEnvironmentAuthenticationIntegrationService,
  FundDataFirmEnvironmentAuthenticationIntegrationService
}
import anduin.funddata.integration.FundDataIntegrationService
import anduin.integplatform.service.external.IntegPlatformExternalService
import anduin.investmententity.service.InvestorProfileFundSubIntegrationService
import anduin.module.*
import anduin.signature.integration.{SignatureIntegrationService, UserSignatureService}

private[testing] object RiaTestModule
    extends GondorTestCommonModule
    with RiaServiceModule
    with HeimdallServiceModule
    with FundSubServiceModule {

  override lazy val dataRoomBotService: DataRoomBotService = DataRoomBotService.Mock()

  override lazy val fundDataIntegrationService: FundDataIntegrationService = FundDataIntegrationService.Mock()

  override lazy val dataExtractIntegrationService: DataExtractIntegrationService = DataExtractIntegrationService.Mock()

  override lazy val dataRoomInternalIntegrationService: DataRoomInternalIntegrationService =
    DataRoomInternalIntegrationService.Mock()

  override lazy val dataRoomExternalIntegrationService: DataRoomExternalIntegrationService =
    DataRoomExternalIntegrationService.Mock()

  override lazy val investorProfileFundSubIntegrationService: InvestorProfileFundSubIntegrationService =
    InvestorProfileFundSubIntegrationService.Mock()

  override lazy val dataRoomEnvironmentAuthenticationIntegrationService
    : DataRoomEnvironmentAuthenticationIntegrationService = DataRoomEnvironmentAuthenticationIntegrationService.Mock()

  override lazy val fundDataFirmEnvironmentAuthenticationIntegrationService
    : FundDataFirmEnvironmentAuthenticationIntegrationService =
    FundDataFirmEnvironmentAuthenticationIntegrationService.Mock()

  override lazy val userSignatureService: UserSignatureService = UserSignatureService.Mock()

  override lazy val signatureIntegrationService: SignatureIntegrationService = SignatureIntegrationService.Mock()

  override lazy val integPlatformExternalService: IntegPlatformExternalService = IntegPlatformExternalService.Mock()
}
