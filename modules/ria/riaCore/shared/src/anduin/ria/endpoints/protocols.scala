// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.ria.endpoints

import io.circe.Codec

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.id.fundsub.ria.FundSubRiaGroupId
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.id.ria.{RiaEntityId, RiaFundGroupId}
import anduin.model.codec.EitherCodec.given
import anduin.model.codec.ProtoCodecs.given
import anduin.model.common.user.{UserId, UserInfo}
import anduin.protobuf.ria.{RiaEntityFundAdvisorRole, RiaEntityUserRole}
import SetupRiaEntityFlow.*
import anduin.ria.endpoints.dashboard.SubscriptionDashboardItem
import anduin.ria.*

final case class UserBasicInfo(
  userId: UserId = UserId.defaultValue.get,
  firstName: String = "",
  lastName: String = "",
  email: String = ""
) {

  lazy val userInfo: UserInfo = UserInfo.DEFAULT.copy(
    firstName = firstName,
    lastName = lastName,
    emailAddressStr = email
  )

  lazy val fullName: String = userInfo.fullNameString
  lazy val displayName: String = userInfo.getDisplayName
}

object UserBasicInfo {
  given Codec.AsObject[UserBasicInfo] = deriveCodecWithDefaults

  def convert(userId: UserId, userInfo: UserInfo): UserBasicInfo = {
    UserBasicInfo(
      userId,
      userInfo.firstName,
      userInfo.lastName,
      userInfo.emailAddressStr
    )
  }

}

final case class CreateAndLinkRiaEntityFromFundInvitationParams(
  name: String,
  creatWithEmailDomain: Boolean,
  fundSubRiaGroupToLink: FundSubRiaGroupId
)

object CreateAndLinkRiaEntityFromFundInvitationParams {
  given Codec.AsObject[CreateAndLinkRiaEntityFromFundInvitationParams] = deriveCodecWithDefaults
}

final case class CreateAndLinkRiaEntityFromFundInvitationResponse(
  riaEntityId: RiaEntityId,
  riaFundGroupId: RiaFundGroupId
)

object CreateAndLinkRiaEntityFromFundInvitationResponse {
  given Codec.AsObject[CreateAndLinkRiaEntityFromFundInvitationResponse] = deriveCodecWithDefaults
}

final case class CreateRiaEntityFromAdminPortalParams(
  name: String,
  emailDomains: Seq[String],
  serviceAccountEmail: Option[String]
)

object CreateRiaEntityFromAdminPortalParams {
  given Codec.AsObject[CreateRiaEntityFromAdminPortalParams] = deriveCodecWithDefaults
}

final case class CreateRiaEntityFromAdminPortalResponse(
  id: RiaEntityId
)

object CreateRiaEntityFromAdminPortalResponse {
  given Codec.AsObject[CreateRiaEntityFromAdminPortalResponse] = deriveCodecWithDefaults
}

final case class UpdateRiaEntityFromAdminPortalParams(
  id: RiaEntityId,
  name: String,
  emailDomains: Seq[String],
  serviceAccountEmail: Option[String]
)

object UpdateRiaEntityFromAdminPortalParams {
  given Codec.AsObject[UpdateRiaEntityFromAdminPortalParams] = deriveCodecWithDefaults
}

final case class UpdateRiaEntityFromAdminPortalResponse()

object UpdateRiaEntityFromAdminPortalResponse {
  given Codec.AsObject[UpdateRiaEntityFromAdminPortalResponse] = deriveCodecWithDefaults
}

final case class UpdateRiaEntitySettingParams(
  id: RiaEntityId,
  name: Option[String] = None,
  emailDomains: Option[Seq[String]] = None,
  enableAutoAddWithEmailDomain: Option[Boolean] = None
)

object UpdateRiaEntitySettingParams {
  given Codec.AsObject[UpdateRiaEntitySettingParams] = deriveCodecWithDefaults
}

final case class UpdateRiaEntitySettingResponse(
  setting: RiaEntitySetting
)

object UpdateRiaEntitySettingResponse {
  given Codec.AsObject[UpdateRiaEntitySettingResponse] = deriveCodecWithDefaults
}

final case class ValidateRiaEntityEmailDomainParams(
  emailDomain: String
)

object ValidateRiaEntityEmailDomainParams {
  given Codec.AsObject[ValidateRiaEntityEmailDomainParams] = deriveCodecWithDefaults
}

final case class ValidateRiaEntityEmailDomainResponse(
  valid: Boolean
)

object ValidateRiaEntityEmailDomainResponse {
  given Codec.AsObject[ValidateRiaEntityEmailDomainResponse] = deriveCodecWithDefaults
}

final case class GetRiaEntityFromAdminPortalParams(
  ids: Seq[RiaEntityId] = Seq.empty // filter by ids
)

object GetRiaEntityFromAdminPortalParams {
  given Codec.AsObject[GetRiaEntityFromAdminPortalParams] = deriveCodecWithDefaults
}

final case class GetRiaEntityFromAdminPortalResponse(
  entities: Seq[RiaEntity]
)

object GetRiaEntityFromAdminPortalResponse {
  given Codec.AsObject[GetRiaEntityFromAdminPortalResponse] = deriveCodecWithDefaults
}

final case class LinkRiaEntityParams(
  riaEntityId: RiaEntityId,
  fundId: FundSubId
)

object LinkRiaEntityParams {
  given Codec.AsObject[LinkRiaEntityParams] = deriveCodecWithDefaults
}

final case class LinkRiaEntityResponse()

object LinkRiaEntityResponse {
  given Codec.AsObject[LinkRiaEntityResponse] = deriveCodecWithDefaults
}

final case class UnlinkRiaEntityParams(
  riaEntityId: RiaEntityId,
  fundId: FundSubId
)

object UnlinkRiaEntityParams {
  given Codec.AsObject[UnlinkRiaEntityParams] = deriveCodecWithDefaults
}

final case class UnlinkRiaEntityResponse()

object UnlinkRiaEntityResponse {
  given Codec.AsObject[UnlinkRiaEntityResponse] = deriveCodecWithDefaults
}

final case class InvitationRiaUserInfo(
  firstName: String,
  lastName: String,
  email: String
)

object InvitationRiaUserInfo {
  given Codec.AsObject[InvitationRiaUserInfo] = deriveCodecWithDefaults
}

final case class InviteRiaEntityFundAdvisorInfo(
  inviteeUser: Either[InvitationRiaUserInfo, UserId],
  fundGroupRole: RiaEntityFundAdvisorRole,
  entityRole: Option[RiaEntityUserRole] = None
)

object InviteRiaEntityFundAdvisorInfo {
  given Codec.AsObject[InviteRiaEntityFundAdvisorInfo] = deriveCodecWithDefaults
}

final case class InviteRiaEntityFundAdvisorParams(
  riaFundGroupId: RiaFundGroupId,
  inviteeUsersInfo: Seq[InviteRiaEntityFundAdvisorInfo]
)

object InviteRiaEntityFundAdvisorParams {
  given Codec.AsObject[InviteRiaEntityFundAdvisorParams] = deriveCodecWithDefaults
}

final case class InviteRiaEntityFundAdvisorResponse()

object InviteRiaEntityFundAdvisorResponse {
  given Codec.AsObject[InviteRiaEntityFundAdvisorResponse] = deriveCodecWithDefaults
}

final case class ResendFundAdvisorInvitationParams(
  riaFundGroupId: RiaFundGroupId,
  toResendInvitationUser: UserId
)

object ResendFundAdvisorInvitationParams {
  given Codec.AsObject[ResendFundAdvisorInvitationParams] = deriveCodecWithDefaults
}

final case class ResendFundAdvisorInvitationResponse()

object ResendFundAdvisorInvitationResponse {
  given Codec.AsObject[ResendFundAdvisorInvitationResponse] = deriveCodecWithDefaults
}

final case class RevokeFundAdvisorInvitationParams(
  riaFundGroupId: RiaFundGroupId,
  toRevokeInvitationUser: UserId
)

object RevokeFundAdvisorInvitationParams {
  given Codec.AsObject[RevokeFundAdvisorInvitationParams] = deriveCodecWithDefaults
}

final case class RevokeFundAdvisorInvitationResponse()

object RevokeFundAdvisorInvitationResponse {
  given Codec.AsObject[RevokeFundAdvisorInvitationResponse] = deriveCodecWithDefaults
}

final case class RemoveRiaEntityFundAdvisorParams(
  riaFundGroupId: RiaFundGroupId,
  toRemoveAdvisor: UserId
)

object RemoveRiaEntityFundAdvisorParams {
  given Codec.AsObject[RemoveRiaEntityFundAdvisorParams] = deriveCodecWithDefaults
}

final case class RemoveRiaEntityFundAdvisorResponse()

object RemoveRiaEntityFundAdvisorResponse {
  given Codec.AsObject[RemoveRiaEntityFundAdvisorResponse] = deriveCodecWithDefaults
}

final case class InviteRiaUserDetail(
  user: Either[InvitationRiaUserInfo, UserId],
  role: RiaEntityUserRole
)

object InviteRiaUserDetail {
  given Codec.AsObject[InviteRiaUserDetail] = deriveCodecWithDefaults
}

final case class InviteRiaUserParams(
  riaEntityId: RiaEntityId,
  riaUserDetails: Seq[InviteRiaUserDetail]
)

object InviteRiaUserParams {
  given Codec.AsObject[InviteRiaUserParams] = deriveCodecWithDefaults
}

final case class InviteRiaUserResponse(
  userIds: Seq[UserId]
)

object InviteRiaUserResponse {
  given Codec.AsObject[InviteRiaUserResponse] = deriveCodecWithDefaults
}

final case class GetCurrentRiaEntityUserRoleParams(
  riaEntityId: RiaEntityId
)

object GetCurrentRiaEntityUserRoleParams {
  given Codec.AsObject[GetCurrentRiaEntityUserRoleParams] = deriveCodecWithDefaults
}

final case class GetCurrentRiaEntityUserRoleResponse(
  role: Option[RiaEntityUserRole],
  permissions: RiaEntityPermissions
)

object GetCurrentRiaEntityUserRoleResponse {
  given Codec.AsObject[GetCurrentRiaEntityUserRoleResponse] = deriveCodecWithDefaults
}

final case class GetCurrentRiaFundGroupAdvisorRoleParams(
  riaFundGroupId: RiaFundGroupId
)

object GetCurrentRiaFundGroupAdvisorRoleParams {
  given Codec.AsObject[GetCurrentRiaFundGroupAdvisorRoleParams] = deriveCodecWithDefaults
}

final case class GetCurrentRiaFundGroupAdvisorRoleResponse(
  role: Option[RiaEntityFundAdvisorRole],
  permissions: RiaEntityPermissions
)

object GetCurrentRiaFundGroupAdvisorRoleResponse {
  given Codec.AsObject[GetCurrentRiaFundGroupAdvisorRoleResponse] = deriveCodecWithDefaults
}

final case class ResendRiaUserInvitationParams(
  riaEntityId: RiaEntityId,
  toResendInvitationUser: UserId
)

object ResendRiaUserInvitationParams {
  given Codec.AsObject[ResendRiaUserInvitationParams] = deriveCodecWithDefaults
}

final case class ResendRiaUserInvitationResponse()

object ResendRiaUserInvitationResponse {
  given Codec.AsObject[ResendRiaUserInvitationResponse] = deriveCodecWithDefaults
}

final case class RemoveRiaUserParams(
  riaEntityId: RiaEntityId,
  toRemoveUser: UserId
)

object RemoveRiaUserParams {
  given Codec.AsObject[RemoveRiaUserParams] = deriveCodecWithDefaults
}

final case class RemoveRiaUserResponse()

object RemoveRiaUserResponse {
  given Codec.AsObject[RemoveRiaUserResponse] = deriveCodecWithDefaults
}

final case class RevokeRiaUserInvitationParams(
  riaEntityId: RiaEntityId,
  toRevokeUser: UserId
)

object RevokeRiaUserInvitationParams {
  given Codec.AsObject[RevokeRiaUserInvitationParams] = deriveCodecWithDefaults
}

final case class RevokeRiaUserInvitationResponse()

object RevokeRiaUserInvitationResponse {
  given Codec.AsObject[RevokeRiaUserInvitationResponse] = deriveCodecWithDefaults
}

final case class EstablishFundLinkageParams(
  riaEntityId: RiaEntityId,
  fundSubRiaGroupToLink: FundSubRiaGroupId
)

object EstablishFundLinkageParams {
  given Codec.AsObject[EstablishFundLinkageParams] = deriveCodecWithDefaults
}

final case class EstablishFundLinkageResponse(
  riaFundGroupId: RiaFundGroupId
)

object EstablishFundLinkageResponse {
  given Codec.AsObject[EstablishFundLinkageResponse] = deriveCodecWithDefaults
}

final case class DeleteRiaEntityParams(
  riaEntityId: RiaEntityId
)

object DeleteRiaEntityParams {
  given Codec.AsObject[DeleteRiaEntityParams] = deriveCodecWithDefaults
}

final case class DeleteRiaEntityResponse()

object DeleteRiaEntityResponse {
  given Codec.AsObject[DeleteRiaEntityResponse] = deriveCodecWithDefaults
}

final case class UpdateRiaEntityUserRoleParams(
  riaEntityId: RiaEntityId,
  toUpdateUser: UserId,
  toUpdateRole: RiaEntityUserRole
)

object UpdateRiaEntityUserRoleParams {
  given Codec.AsObject[UpdateRiaEntityUserRoleParams] = deriveCodecWithDefaults
}

final case class UpdateRiaEntityUserRoleResponse()

object UpdateRiaEntityUserRoleResponse {
  given Codec.AsObject[UpdateRiaEntityUserRoleResponse] = deriveCodecWithDefaults
}

final case class UpdateRiaFundGroupAdvisorRoleParams(
  riaFundGroupId: RiaFundGroupId,
  toUpdateAdvisor: UserId,
  toUpdateRole: RiaEntityFundAdvisorRole
)

object UpdateRiaFundGroupAdvisorRoleParams {
  given Codec.AsObject[UpdateRiaFundGroupAdvisorRoleParams] = deriveCodecWithDefaults
}

final case class UpdateRiaFundGroupAdvisorRoleResponse()

object UpdateRiaFundGroupAdvisorRoleResponse {
  given Codec.AsObject[UpdateRiaFundGroupAdvisorRoleResponse] = deriveCodecWithDefaults
}

final case class JoinRiaEntityParams(
  riaEntityId: RiaEntityId
)

object JoinRiaEntityParams {
  given Codec.AsObject[JoinRiaEntityParams] = deriveCodecWithDefaults
}

final case class JoinRiaEntityResponse()

object JoinRiaEntityResponse {
  given Codec.AsObject[JoinRiaEntityResponse] = deriveCodecWithDefaults
}

final case class ConvertToRiaOrderParams(
  riaFundGroupId: RiaFundGroupId,
  toConvertOrders: Seq[FundSubLpId]
)

object ConvertToRiaOrderParams {
  given Codec.AsObject[ConvertToRiaOrderParams] = deriveCodecWithDefaults
}

final case class ConvertToRiaOrderResponse()

object ConvertToRiaOrderResponse {
  given Codec.AsObject[ConvertToRiaOrderResponse] = deriveCodecWithDefaults
}

final case class HideConvertToRiaOrderBannerParams(
  riaFundGroupId: RiaFundGroupId
)

object HideConvertToRiaOrderBannerParams {
  given Codec.AsObject[HideConvertToRiaOrderBannerParams] = deriveCodecWithDefaults
}

final case class HideConvertToRiaOrderBannerResponse()

object HideConvertToRiaOrderBannerResponse {
  given Codec.AsObject[HideConvertToRiaOrderBannerResponse] = deriveCodecWithDefaults
}

final case class GetConvertibleOrderDetailParams(
  riaFundGroupId: RiaFundGroupId
)

object GetConvertibleOrderDetailParams {
  given Codec.AsObject[GetConvertibleOrderDetailParams] = deriveCodecWithDefaults
}

final case class GetConvertibleOrderDetailResponse(
  convertibleOrders: Seq[SubscriptionDashboardItem]
)

object GetConvertibleOrderDetailResponse {
  given Codec.AsObject[GetConvertibleOrderDetailResponse] = deriveCodecWithDefaults
}

final case class GetConvertibleOrderParams(
  riaFundGroupId: RiaFundGroupId
)

object GetConvertibleOrderParams {
  given Codec.AsObject[GetConvertibleOrderParams] = deriveCodecWithDefaults
}

final case class GetConvertibleOrderResponse(
  orderIds: Seq[FundSubLpId],
  shouldShowBanner: Boolean
)

object GetConvertibleOrderResponse {
  given Codec.AsObject[GetConvertibleOrderResponse] = deriveCodecWithDefaults
}

final case class HandleHowUserJoinFromFundInvitationParams(
  fundSubRiaGroupId: FundSubRiaGroupId
)

object HandleHowUserJoinFromFundInvitationParams {
  given Codec.AsObject[HandleHowUserJoinFromFundInvitationParams] = deriveCodecWithDefaults
}

sealed trait SetupRiaEntityFlow derives CanEqual

object SetupRiaEntityFlow {
  given Codec.AsObject[SetupRiaEntityFlow] = deriveCodecWithDefaults

  case object Invalid extends SetupRiaEntityFlow
  given Codec.AsObject[Invalid.type] = deriveCodecWithDefaults

  case object CreateEntity extends SetupRiaEntityFlow
  given Codec.AsObject[CreateEntity.type] = deriveCodecWithDefaults

  final case class SelectOrCreateEntity(
    entitiesToSelect: Seq[RiaEntityInfoToLink]
  ) extends SetupRiaEntityFlow

  object SelectOrCreateEntity {
    given Codec.AsObject[SelectOrCreateEntity] = deriveCodecWithDefaults
  }

  case class SuggestContactEntityAdmin(
    suggestEntityName: String,
    suggestEntityAdminEmails: Seq[String],
    entitiesToSelect: Seq[RiaEntityInfoToLink]
  ) extends SetupRiaEntityFlow

  given Codec.AsObject[SuggestContactEntityAdmin] = deriveCodecWithDefaults

  final case class SkipToFundGroupScreen(riaFundGroupId: RiaFundGroupId) extends SetupRiaEntityFlow

  object SkipToFundGroupScreen {
    given Codec.AsObject[SkipToFundGroupScreen] = deriveCodecWithDefaults
  }

}

final case class HandleHowUserJoinFromFundInvitationResponse(
  screen: SetupRiaEntityFlow,
  fundName: String
)

object HandleHowUserJoinFromFundInvitationResponse {
  given Codec.AsObject[HandleHowUserJoinFromFundInvitationResponse] = deriveCodecWithDefaults
}

final case class GetRiaEntityPublicInfoParams(
  riaEntityId: RiaEntityId
)

object GetRiaEntityPublicInfoParams {
  given Codec.AsObject[GetRiaEntityPublicInfoParams] = deriveCodecWithDefaults
}

final case class RiaEntityPublicInfo(
  id: RiaEntityId,
  name: String,
  emailDomains: Seq[String]
)

object RiaEntityPublicInfo {
  given Codec.AsObject[RiaEntityPublicInfo] = deriveCodecWithDefaults
}

final case class GetRiaEntityPublicInfoResponse(
  info: RiaEntityPublicInfo
)

object GetRiaEntityPublicInfoResponse {
  given Codec.AsObject[GetRiaEntityPublicInfoResponse] = deriveCodecWithDefaults
}

final case class GetAccessibleLinkedFundsParams(
  getBy: Either[RiaEntityId, RiaFundGroupId]
)

object GetAccessibleLinkedFundsParams {
  given Codec.AsObject[GetAccessibleLinkedFundsParams] = deriveCodecWithDefaults
}

final case class GetAccessibleLinkedFundsResponse(
  linkedFunds: Seq[LinkedFund]
)

object GetAccessibleLinkedFundsResponse {
  given Codec.AsObject[GetAccessibleLinkedFundsResponse] = deriveCodecWithDefaults
}

final case class GetAccessibleRiaEntitiesParams(
  getBy: Option[Either[RiaEntityId, RiaFundGroupId]] = None
)

object GetAccessibleRiaEntitiesParams {
  given Codec.AsObject[GetAccessibleRiaEntitiesParams] = deriveCodecWithDefaults
}

final case class GetAccessibleRiaEntitiesResponse(
  riaEntities: Seq[RiaEntity]
)

object GetAccessibleRiaEntitiesResponse {
  given Codec.AsObject[GetAccessibleRiaEntitiesResponse] = deriveCodecWithDefaults
}

final case class GetAccessibleRiaEntityUsersParams(
  riaEntityId: RiaEntityId
)

object GetAccessibleRiaEntityUsersParams {
  given Codec.AsObject[GetAccessibleRiaEntityUsersParams] = deriveCodecWithDefaults
}

final case class GetAccessibleRiaEntityUsersResponse(
  users: Seq[RiaUser]
)

object GetAccessibleRiaEntityUsersResponse {
  given Codec.AsObject[GetAccessibleRiaEntityUsersResponse] = deriveCodecWithDefaults
}

final case class GetAccessibleLinkedFundAdvisorsParams(
  riaFundGroupId: RiaFundGroupId
)

object GetAccessibleLinkedFundAdvisorsParams {
  given Codec.AsObject[GetAccessibleLinkedFundAdvisorsParams] = deriveCodecWithDefaults
}

final case class GetAccessibleLinkedFundAdvisorsResponse(
  advisors: Seq[Advisor]
)

object GetAccessibleLinkedFundAdvisorsResponse {
  given Codec.AsObject[GetAccessibleLinkedFundAdvisorsResponse] = deriveCodecWithDefaults
}

final case class GetRiaEntitySettingParams(
  riaEntityId: RiaEntityId
)

object GetRiaEntitySettingParams {
  given Codec.AsObject[GetRiaEntitySettingParams] = deriveCodecWithDefaults
}

final case class GetRiaEntitySettingResponse(
  setting: RiaEntitySetting
)

object GetRiaEntitySettingResponse {
  given Codec.AsObject[GetRiaEntitySettingResponse] = deriveCodecWithDefaults
}

final case class CreateSubscriptionInfo(
  investmentEntityName: String,
  advisorUserId: UserId
)

object CreateSubscriptionInfo {
  given Codec.AsObject[CreateSubscriptionInfo] = deriveCodecWithDefaults
}

final case class CreateSubscriptionParams(
  riaFundGroupId: RiaFundGroupId,
  subscriptions: Seq[CreateSubscriptionInfo]
)

object CreateSubscriptionParams {
  given Codec.AsObject[CreateSubscriptionParams] = deriveCodecWithDefaults
}

final case class CreateSubscriptionResponse(
  lpIds: Seq[FundSubLpId],
  failedMessages: Seq[String]
)

object CreateSubscriptionResponse {
  given Codec.AsObject[CreateSubscriptionResponse] = deriveCodecWithDefaults
}

final case class GetParticipatedRiaEntitiesParams()

object GetParticipatingRiaEntitiesParams {
  given Codec.AsObject[GetParticipatedRiaEntitiesParams] = deriveCodecWithDefaults
}

final case class GetParticipatedRiaEntitiesResponse(
  riaEntities: Seq[RiaEntitySimpleInfo]
)

object GetParticipatedRiaEntitiesResponse {
  given Codec.AsObject[GetParticipatedRiaEntitiesResponse] = deriveCodecWithDefaults
}

final case class UnlinkRiaOrderParams(
  riaFundGroupId: RiaFundGroupId,
  orderId: FundSubLpId
)

object UnlinkRiaOrderParams {
  given Codec.AsObject[UnlinkRiaOrderParams] = deriveCodecWithDefaults
}

final case class UnlinkRiaOrderResponse()

object UnlinkRiaOrderResponse {
  given Codec.AsObject[UnlinkRiaOrderResponse] = deriveCodecWithDefaults
}

sealed trait EmailDomainCheckResult derives CanEqual

object EmailDomainCheckResult {
  given Codec.AsObject[EmailDomainCheckResult] = deriveCodecWithDefaults

  case class MatchWithAnEntity(
    riaEntityId: RiaEntityId,
    riaEntityName: String
  ) extends EmailDomainCheckResult

  given Codec.AsObject[MatchWithAnEntity] = deriveCodecWithDefaults

  case object IsPublicDomain extends EmailDomainCheckResult
  given Codec.AsObject[IsPublicDomain.type] = deriveCodecWithDefaults

  case object IsAvailable extends EmailDomainCheckResult
  given Codec.AsObject[IsAvailable.type] = deriveCodecWithDefaults
}

final case class CheckIfCurrentUserEmailDomainAvailableResponse(
  result: EmailDomainCheckResult
)

object CheckIfCurrentUserEmailDomainAvailableResponse {
  given Codec.AsObject[CheckIfCurrentUserEmailDomainAvailableResponse] = deriveCodecWithDefaults
}
