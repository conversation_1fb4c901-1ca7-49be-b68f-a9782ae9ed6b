syntax = "proto3";

package anduin.workflow.fundsub.simulator;

import "scalapb/scalapb.proto";
import "fundsub/simulator/model.proto";
import "signature/e_signature.proto";

option (scalapb.options) = {
  package_name: "anduin.workflow.fundsub.simulator"
  single_file: true
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.entity.EntityId"
  import: "anduin.id.fundsub.CustomDataColumnId"
  import: "anduin.model.id.stage.DataRoomWorkflowId"
  import: "anduin.id.link.ProtectedLinkId"
};

enum DemoFormTypeProto {
  USDemoForm = 0;
  LuxDemoForm = 1;
  LightLogicForm = 2;
  LpTransferForm = 3;
  MasterSideLetterForm = 4;
  OpenEndedForm = 5;
  DataExtractFund = 6;
}

message SetupDemoOrdersParams {
  reserved 9, 11, 12;
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
  string fund_sub_id = 2 [(scalapb.field).type = "FundSubId"];
  string actor = 3 [(scalapb.field).type = "UserId"];
  string email = 4;
  anduin.fundsub.simulator.DemoFundData fund_data = 5;
  bool should_send_confirm_email = 6;
  repeated string custom_data_column_ids = 7 [(scalapb.field).type = "CustomDataColumnId", (scalapb.field).collection_type = "List"];
  bool should_disable_soft_review = 8;
  DemoFormTypeProto demo_form_type = 10;
  string link_id = 13 [(scalapb.field).type = "ProtectedLinkId"];
  repeated anduin.fundsub.simulator.AdditionalFundInfo additional_fund_infos = 14;
}

message EmptyResponse {}

message SetupDemoSignatureParams {
  string actor = 1 [(scalapb.field).type = "UserId"];
  anduin.protobuf.signature.OldSignatureMessage signature_message = 2;
}

message CreateDemoLpsParams {
  string actor = 1 [(scalapb.field).type = "UserId"];
  string fund_sub_id = 2 [(scalapb.field).type = "FundSubId"];
  anduin.fundsub.simulator.DemoFundData fund_data = 3;
  repeated string custom_data_column_ids = 4 [(scalapb.field).type = "CustomDataColumnId", (scalapb.field).collection_type = "List"];
  bool should_disable_soft_review = 5;
  string main_fund_sub_id = 6 [(scalapb.field).type = "FundSubId"];
  int32 current_progress = 7;
  int32 num_total_orders = 8; // Total orders of main Fund and Additional Funds
}

message CreatedDemoLps {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string lp_investment_entity_name = 2;
  bool is_main_lp_demo = 3;
  bool is_additional_lp_demo = 4;
}

message CreatedDemoFundSub {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  string fund_sub_name = 2;
  string sharable_link_id = 3 [(scalapb.field).type = "ProtectedLinkId"];
}

message CreatedDemoDataRoom {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string data_room_name = 2;
  string link_id = 3 [(scalapb.field).type = "ProtectedLinkId"];
}

message CreateDemoLpsResponse {
  repeated CreatedDemoLps created_lps = 1;
}

message InitializeRequestChangesCommentParams {
  string actor = 1 [(scalapb.field).type = "UserId"];
  string fund_sub_id = 2 [(scalapb.field).type = "FundSubId"];
  repeated anduin.fundsub.simulator.RequestChangeCommentData request_changes_comment_data = 3;
}

message ResetDemoSignatureParams {
  string actor = 1 [(scalapb.field).type = "UserId"];
}

message PrepareGroupAndDashboardParams {
  reserved 3, 4;
  string actor = 1 [(scalapb.field).type = "UserId"];
  string fund_sub_id = 2 [(scalapb.field).type = "FundSubId"];
  repeated string custom_data_column_ids = 5 [(scalapb.field).type = "CustomDataColumnId", (scalapb.field).collection_type = "List"];
}

message CompleteSetupDemoOrdersParams {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  repeated string additional_fund_sub_ids = 2 [(scalapb.field).type = "FundSubId"];
}

message SetupDemoFundDataParams {
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
  string fund_sub_id = 2 [(scalapb.field).type = "FundSubId"];
  repeated CreatedDemoLps created_lps = 3;
  string actor = 4 [(scalapb.field).type = "UserId"];
  repeated CreatedDemoDataRoom created_data_rooms = 5;
  repeated CreatedDemoFundSub created_fund_subs = 6;
}

message SetupMarketingDataRoomParams {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string actor_email = 2;
  string entity_id = 3 [(scalapb.field).type = "EntityId"];
}

message SetupMarketingDataRoomResponse {
  repeated CreatedDemoDataRoom created_data_rooms = 1;
}

message SetupDemoOrdersErrorParams {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

