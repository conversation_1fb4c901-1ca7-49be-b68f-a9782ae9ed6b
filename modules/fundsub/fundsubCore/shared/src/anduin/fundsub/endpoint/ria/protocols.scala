// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.endpoint.ria

import io.circe.Codec

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.fundsub.endpoint.ria.RiaFundGroupState.InvitedAdvisor
import anduin.id.fundsub.ria.FundSubRiaGroupId
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.id.ria.{RiaEntityId, RiaFundGroupId}
import anduin.model.codec.EitherCodec.given
import anduin.model.codec.ProtoCodecs.given
import anduin.model.common.user.{UserId, UserIdAndUserInfo}
import anduin.protobuf.ria.RiaEntityFundAdvisorRole
import java.time.Instant

import anduin.fundsub.endpoint.ria.RiaFundGroupState.FundGroupAdvisor

final case class InviteAdvisor(
  firstName: String = "",
  lastName: String = "",
  email: String = ""
)

object InviteAdvisor {
  given Codec.AsObject[InviteAdvisor] = deriveCodecWithDefaults
}

final case class CreateRiaFundGroupParams(
  fundSubId: FundSubId,
  name: String,
  inviteAdvisors: Seq[InviteAdvisor]
)

object CreateRiaFundGroupParams {
  given Codec.AsObject[CreateRiaFundGroupParams] = deriveCodecWithDefaults
}

final case class CreateRiaFundGroupResponse(
  fundRiaGroupId: FundSubRiaGroupId
)

object CreateRiaFundGroupResponse {
  given Codec.AsObject[CreateRiaFundGroupResponse] = deriveCodecWithDefaults
}

final case class RemoveRiaFundGroupParams(
  fundRiaGroupId: FundSubRiaGroupId
)

object RemoveRiaFundGroupParams {
  given Codec.AsObject[RemoveRiaFundGroupParams] = deriveCodecWithDefaults
}

final case class RemoveRiaFundGroupResponse()

object RemoveRiaFundGroupResponse {
  given Codec.AsObject[RemoveRiaFundGroupResponse] = deriveCodecWithDefaults
}

final case class UpdateRiaFundGroupParams(
  fundRiaGroupId: FundSubRiaGroupId,
  name: String
)

object UpdateRiaFundGroupParams {
  given Codec.AsObject[UpdateRiaFundGroupParams] = deriveCodecWithDefaults
}

final case class UpdateRiaFundGroupResponse()

object UpdateRiaFundGroupResponse {
  given Codec.AsObject[UpdateRiaFundGroupResponse] = deriveCodecWithDefaults
}

final case class GetRiaFundGroupParams(
  getBy: Either[FundSubRiaGroupId, FundSubId]
)

object GetRiaFundGroupParams {
  given Codec.AsObject[GetRiaFundGroupParams] = deriveCodecWithDefaults
}

final case class FundSubLinkedRiaEntity(
  id: RiaEntityId,
  name: String,
  emailDomains: Seq[String],
  advisors: Seq[FundSubRiaEntityAdvisor]
) {
  val orders: Seq[FundSubLpId] = advisors.flatMap(_.subscriptions)
}

object FundSubLinkedRiaEntity {
  given Codec.AsObject[FundSubLinkedRiaEntity] = deriveCodecWithDefaults
}

final case class FundSubRiaEntityAdvisor(
  info: UserIdAndUserInfo,
  subscriptions: Seq[FundSubLpId],
  role: RiaEntityFundAdvisorRole,
  joinedAt: Option[Instant],
  invitedAt: Option[Instant],
  addedBy: Option[UserIdAndUserInfo]
) extends FundGroupAdvisor

object FundSubRiaEntityAdvisor {
  given Codec.AsObject[FundSubRiaEntityAdvisor] = deriveCodecWithDefaults
}

sealed trait RiaFundGroupState

object RiaFundGroupState {
  given Codec.AsObject[RiaFundGroupState] = deriveCodecWithDefaults

  sealed trait FundGroupAdvisor {
    val info: UserIdAndUserInfo
  }

  object FundGroupAdvisor {
    given Codec.AsObject[FundGroupAdvisor] = deriveCodecWithDefaults
  }

  final case class RiaEntityNotLinked(
    invitedAdvisors: Seq[InvitedAdvisor]
  ) extends RiaFundGroupState

  final case class InvitedAdvisor(
    info: UserIdAndUserInfo,
    invitedAt: Option[Instant]
  ) extends FundGroupAdvisor

  object InvitedAdvisor {
    given Codec.AsObject[InvitedAdvisor] = deriveCodecWithDefaults
  }

  object RiaEntityNotLinked {

    given Codec.AsObject[RiaEntityNotLinked] = deriveCodecWithDefaults
  }

  final case class RiaEntityLinked(
    linkedRiaEntity: FundSubLinkedRiaEntity,
    invalidAdvisors: Seq[InvitedAdvisor]
  ) extends RiaFundGroupState {

    val numberOfOrder: Int = linkedRiaEntity.orders.size

  }

  object RiaEntityLinked {
    given Codec.AsObject[RiaEntityLinked] = deriveCodecWithDefaults
  }

}

final case class RiaFundGroup(
  id: FundSubRiaGroupId,
  name: String,
  canCreateOrder: Boolean,
  state: RiaFundGroupState,
  createdAt: Option[Instant],
  createdBy: UserIdAndUserInfo
) {

  val isLinked: Boolean = state match {
    case _: RiaFundGroupState.RiaEntityNotLinked => false
    case _: RiaFundGroupState.RiaEntityLinked    => true
  }

  val members: Seq[FundGroupAdvisor] = state match {
    case s: RiaFundGroupState.RiaEntityNotLinked => s.invitedAdvisors
    case s: RiaFundGroupState.RiaEntityLinked    => s.linkedRiaEntity.advisors
  }

  val numberOfMembers: Int = members.size

}

object RiaFundGroup {
  given Codec.AsObject[RiaFundGroup] = deriveCodecWithDefaults
}

final case class GetRiaFundGroupResponse(
  fundRiaGroups: Seq[RiaFundGroup]
)

object GetRiaFundGroupResponse {
  given Codec.AsObject[GetRiaFundGroupResponse] = deriveCodecWithDefaults
}

final case class InviteFundAdvisorParams(
  fundRiaGroupId: FundSubRiaGroupId,
  inviteAdvisors: Seq[InviteAdvisor]
)

object InviteFundAdvisorParams {
  given Codec.AsObject[InviteFundAdvisorParams] = deriveCodecWithDefaults
}

final case class InviteFundAdvisorResponse()

object InviteFundAdvisorResponse {
  given Codec.AsObject[InviteFundAdvisorResponse] = deriveCodecWithDefaults
}

final case class UpdateAdvisorAbilityToCreateOrderParams(
  shouldAllowCreatingOrder: Boolean
)

final case class ResendFundAdvisorInvitationParams(
  fundSubRiaGroupId: FundSubRiaGroupId,
  advisor: UserId
)

object ResendFundAdvisorInvitationParams {
  given Codec.AsObject[ResendFundAdvisorInvitationParams] = deriveCodecWithDefaults
}

final case class ResendFundAdvisorInvitationResponse()

object ResendFundAdvisorInvitationResponse {
  given Codec.AsObject[ResendFundAdvisorInvitationResponse] = deriveCodecWithDefaults
}

final case class RevokeFundAdvisorInvitationParams(
  fundSubRiaGroupId: FundSubRiaGroupId,
  advisor: UserId
)

object RevokeFundAdvisorInvitationParams {
  given Codec.AsObject[RevokeFundAdvisorInvitationParams] = deriveCodecWithDefaults
}

final case class RevokeFundAdvisorInvitationResponse()

object RevokeFundAdvisorInvitationResponse {
  given Codec.AsObject[RevokeFundAdvisorInvitationResponse] = deriveCodecWithDefaults
}

object UpdateAdvisorAbilityToCreateOrderParams {
  given Codec.AsObject[UpdateAdvisorAbilityToCreateOrderParams] = deriveCodecWithDefaults
}

final case class UpdateAdvisorAbilityToCreateOrderResponse()

object UpdateAdvisorAbilityToCreateOrderResponse {
  given Codec.AsObject[UpdateAdvisorAbilityToCreateOrderResponse] = deriveCodecWithDefaults
}

final case class ManageAbilityToCreateOrderParams(
  fundSubRiaGroupId: FundSubRiaGroupId,
  isAllow: Boolean
)

object ManageAbilityToCreateOrderParams {
  given Codec.AsObject[ManageAbilityToCreateOrderParams] = deriveCodecWithDefaults
}

final case class ManageAbilityToCreateOrderResponse()

object ManageAbilityToCreateOrderResponse {
  given Codec.AsObject[ManageAbilityToCreateOrderResponse] = deriveCodecWithDefaults
}

final case class EstablishRiaEntityLinkageParams(
  fundSubRiaGroupId: FundSubRiaGroupId,
  riaFundGroupId: RiaFundGroupId,
  riaEntityEmailDomains: Seq[String]
)

object EstablishRiaEntityLinkageParams {
  given Codec.AsObject[EstablishRiaEntityLinkageParams] = deriveCodecWithDefaults
}

final case class ToTransferAdvisor(
  userId: UserId,
  invitedAt: Option[Instant],
  invitedBy: UserId
)

object ToTransferAdvisor {
  given Codec.AsObject[ToTransferAdvisor] = deriveCodecWithDefaults
}

final case class EstablishRiaEntityLinkageResponse(
  toTransferAdvisors: Seq[ToTransferAdvisor]
)

object EstablishRiaEntityLinkageResponse {
  given Codec.AsObject[EstablishRiaEntityLinkageResponse] = deriveCodecWithDefaults
}
