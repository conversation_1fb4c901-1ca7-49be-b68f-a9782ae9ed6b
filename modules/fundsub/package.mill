package build.modules.fundsub

import build_.modules.fundsub.dependency.FundSub
import build_.build.util._
import build.modules.{
  dynamicForm,
  heimdall,
  fundsubFormData,
  amlKyc,
  gaia,
  dataextract,
  greylin,
  evendim,
  investorProfile,
  signature,
  brienne,
  ria,
  dataroom,
  fundData,
  dynamicFormTest,
  bifrost,
  integplatform
}
import build.platform.{stargazer, stargazerCore}

import mill._
import mill.scalalib._
import anduin.build._
import anduin.mill._
import anduin.mill.copy._

object `package` extends RootModule {

  object fundsubModel extends StargazerModelCrossPlatformModule {

    object jvm extends JvmModelModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        dynamicForm.dynamicFormModel.jvm,
        build.gondor.gondorModel.jvm,
        heimdall.heimdallModel.jvm,
        fundsubFormData.fundsubFormDataModel,
        amlKyc.amlKycModel.jvm
      )

    }

    object js extends JsModelModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        stargazerCore.js,
        signature.signatureModel.js,
        build.gondor.gondorModel.js,
        dynamicForm.dynamicFormModel.js,
        amlKyc.amlKycModel.js
      )

    }

    trait SharedModule extends ScalaModule {
      override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")
    }

  }

  object fundsubCore extends AnduinCrossPlatformModule {

    object jvm extends JvmModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        evendim,
        dynamicForm.dynamicForm.jvm,
        gaia.gaia.jvm,
        fundsubFormData.fundsubFormData,
        investorProfile.investorProfileCore.jvm,
        build.gondor.gondorCore.jvm,
        greylin.greylinCore,
        dataextract.dataextractCore.jvm,
        amlKyc.amlKycCore.jvm,
        ria.riaModel.jvm,
        integplatform.integplatformModel.jvm
      )

    }

    object js extends JsModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        dynamicForm.dynamicForm.js,
        build.gondor.gondorCore.js,
        gaia.gaia.js,
        investorProfile.investorProfileCore.js,
        dataextract.dataextractCore.js,
        amlKyc.amlKycCore.js,
        ria.riaModel.js,
        integplatform.integplatformModel.js
      )

    }

    trait SharedModule extends AnduinCopyModule {
      override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")
      override def copySources: T[Seq[PathRef]] = Task.Sources(moduleDir / os.up / "shared" / "copy")
    }

  }

  object fundsub extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {

      override def ivyDeps =
        super.ivyDeps() ++ FundSub.jvmDeps

      override def moduleDeps = super.moduleDeps ++ Seq(
        dataroom.dataroomIntegration.jvm,
        fundsubCore.jvm,
        investorProfile.investorProfile.jvm,
        build.gondor.gondorCore.jvm,
        greylin.greylinCore,
        bifrost.jvm,
        brienne.brienneCore.jvm,
        fundData.fundDataCore.jvm,
        integplatform.integplatformCore.jvm,
        dataextract.dataextractIntegration.jvm,
        ria.riaCore.jvm
      )

      object it extends AnduinZioTests with AnduinIntegTests {
        override def moduleDeps = super.moduleDeps ++ Seq(fundsubTest, signature.signature.jvm)
      }

    }

    object js extends JsModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        dynamicFormTest.dynamicFormTest.js,
        fundsubCore.js,
        fundData.fundDataCore.js,
        integplatform.integplatformCore.js,
        build.gondor.gondorCore.js,
        bifrost.js
      )

    }

  }

  object fundsubTest extends AnduinScalaModule {
    override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorTest, fundsub.jvm)
  }

  object fundsubApp extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {
      override def moduleDeps = super.moduleDeps ++ Seq(fundsub.jvm)
    }

    object js extends JsModule with AnduinWebClientModule {
      override def webModule = AnduinWebModules.FundSub

      override def moduleDeps = super.moduleDeps ++ Seq(fundsub.js)

      override def mainClass = Some("com.anduin.stargazer.fundsub.client.FundSubMainApp")
    }

  }

}
