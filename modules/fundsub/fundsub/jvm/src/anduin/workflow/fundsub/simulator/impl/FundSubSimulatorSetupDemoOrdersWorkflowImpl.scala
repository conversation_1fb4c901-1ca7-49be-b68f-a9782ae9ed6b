// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.simulator.impl

import java.time.Duration

import zio.temporal.ZRetryOptions
import zio.temporal.activity.{ZActivityOptions, ZActivityStub}
import zio.temporal.failure.ActivityFailure
import zio.temporal.workflow.ZWorkflow

import anduin.fundsub.simulator.model.DemoFundData
import anduin.id.fundsub.{CustomDataColumnId, FundSubId}
import anduin.model.common.user.UserId
import anduin.workflow.fundsub.simulator.*
import anduin.workflow.{ActivityQueue, WorkflowImpl, WorkflowQueue}

class FundSubSimulatorSetupDemoOrdersWorkflowImpl extends FundSubSimulatorSetupDemoOrdersWorkflow {

  private val activities = ZWorkflow
    .newActivityStub[FundSubSimulatorSetupDemoOrdersActivities](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofMinutes(30))
        .withTaskQueue(ActivityQueue.FundSubSimulator.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(1))
    )

  private def errorHandler(fundSubId: FundSubId, e: Throwable) = {
    scribe.error(s"Failed to set up demo orders for fund ${fundSubId.idString}", e)
    ZActivityStub.execute(activities.setupDemoOrdersError(SetupDemoOrdersErrorParams(fundSubId)))
    EmptyResponse()
  }

  private def setupDemoOrdersForSingleFundSub(
    actor: UserId,
    fundSubId: FundSubId,
    fundData: Option[DemoFundData],
    customDataColumnIds: List[CustomDataColumnId],
    shouldDisableSoftReview: Boolean,
    demoFormType: DemoFormTypeProto,
    mainFundSubId: FundSubId, // The progress bar is tracked by the mainFundSubId
    currentProgress: Int,
    numTotalOrders: Int
  ): CreateDemoLpsResponse = {
    scribe.info(s"Start setting up demo orders for single fund ${fundSubId.idString}")

    val createDemoLpsResponse = ZActivityStub.execute(
      activities.createDemoLps(
        CreateDemoLpsParams(
          actor = actor,
          fundSubId = fundSubId,
          fundData = fundData,
          customDataColumnIds = customDataColumnIds,
          shouldDisableSoftReview = shouldDisableSoftReview,
          mainFundSubId = mainFundSubId,
          currentProgress = currentProgress,
          numTotalOrders = numTotalOrders
        )
      )
    )
    ZActivityStub.execute(
      activities.initializeRequestChangesComment(
        InitializeRequestChangesCommentParams(
          actor,
          fundSubId,
          fundData.map(_.requestChangeCommentData).getOrElse(Seq.empty)
        )
      )
    )
    ZActivityStub.execute(activities.resetDemoSignature(ResetDemoSignatureParams(actor)))
    demoFormType match {
      case DemoFormTypeProto.USDemoForm | DemoFormTypeProto.LuxDemoForm =>
        ZActivityStub.execute(
          activities.prepareGroupAndDashboard(
            PrepareGroupAndDashboardParams(
              actor = actor,
              fundSubId = fundSubId,
              customDataColumnIds = customDataColumnIds
            )
          )
        )
      case _ => ()
    }
    createDemoLpsResponse
  }

  override def setupDemoOrders(params: SetupDemoOrdersParams): EmptyResponse = {
    scribe.info(s"Start setting up demo orders for fund ${params.fundSubId.idString}")
    try {
      ZActivityStub.execute(activities.setupDemoSignature(SetupDemoSignatureParams(params.actor)))
      val numTotalOrders =
        params.fundData.map(_.demoOrders.size).getOrElse(0) + params.additionalFundInfos
          .map(_.fundData.map(_.demoOrders.size).getOrElse(0))
          .sum
      val mainLps = setupDemoOrdersForSingleFundSub(
        actor = params.actor,
        fundSubId = params.fundSubId,
        fundData = params.fundData,
        customDataColumnIds = params.customDataColumnIds,
        shouldDisableSoftReview = params.shouldDisableSoftReview,
        demoFormType = params.demoFormType,
        mainFundSubId = params.fundSubId,
        currentProgress = 0,
        numTotalOrders = numTotalOrders
      )

      val (_, additionalLps) = params.additionalFundInfos
        .foldLeft[(Int, Seq[CreateDemoLpsResponse])]((params.fundData.map(_.demoOrders.size).getOrElse(0), Seq.empty)) {
          case ((currentProgress, results), fundInfo) =>
            val createdLps = setupDemoOrdersForSingleFundSub(
              actor = params.actor,
              fundSubId = fundInfo.fundSubId,
              fundData = fundInfo.fundData,
              customDataColumnIds = params.customDataColumnIds,
              shouldDisableSoftReview = params.shouldDisableSoftReview,
              demoFormType = params.demoFormType,
              mainFundSubId = params.fundSubId,
              currentProgress = currentProgress,
              numTotalOrders = numTotalOrders
            )
            (currentProgress + fundInfo.fundData.map(_.demoOrders.size).getOrElse(0), results :+ createdLps)
        }

      val createdMainFundSub = CreatedDemoFundSub(
        fundSubId = params.fundSubId,
        fundSubName = params.fundData.map(_.fundName).getOrElse(""),
        sharableLinkId = params.linkId
      )
      val createdAdditionalFundSubs = params.additionalFundInfos.map { fundInfo =>
        CreatedDemoFundSub(
          fundSubId = fundInfo.fundSubId,
          fundSubName = fundInfo.fundData.map(_.fundName).getOrElse(""),
          sharableLinkId = fundInfo.linkId
        )
      }

      params.demoFormType match {
        case DemoFormTypeProto.USDemoForm => // Only Create FDM & Marketing DR Demo for US demo form
          val createdMarketingDataRooms =
            mainLps.createdLps.filter(_.isAdditionalLpDemo).map(_.fundSubLpId).flatMap { fundSubLpId =>
              ZActivityStub
                .execute(
                  activities.setUpMarketingDataRoom(
                    SetupMarketingDataRoomParams(
                      fundSubLpId = fundSubLpId,
                      actorEmail = params.email,
                      entityId = params.entityId
                    )
                  )
                )
                .createdDataRooms
            }
          ZActivityStub.execute(
            activities.setUpDemoFundData(
              SetupDemoFundDataParams(
                entityId = params.entityId,
                fundSubId = params.fundSubId,
                createdLps = mainLps.createdLps ++ additionalLps.flatMap(_.createdLps),
                createdDataRooms = createdMarketingDataRooms,
                createdFundSubs = createdMainFundSub +: createdAdditionalFundSubs,
                actor = params.actor
              )
            )
          )
        case _ => ()
      }
      ZActivityStub.execute(activities.completeSetupDemoOrders(CompleteSetupDemoOrdersParams(params.fundSubId)))

    } catch {
      case e: ActivityFailure => errorHandler(params.fundSubId, e)
    }
  }

}

object FundSubSimulatorSetupDemoOrdersWorkflowImpl {

  lazy val instance = WorkflowImpl[
    FundSubSimulatorSetupDemoOrdersWorkflow,
    FundSubSimulatorSetupDemoOrdersWorkflowImpl
  ](WorkflowQueue.FundSubSimulator)

}
