// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.simulator.impl

import zio.ZIO

import anduin.fdb.record.FDBRecordDatabase
import anduin.funddata.simulator.FundDataSimulatorService
import anduin.fundsub.models.FundSubModelStoreOperations
import anduin.fundsub.simulator.FundSubSimulatorService
import anduin.fundsub.view.FundSubViewService
import anduin.protobuf.fundsub.models.FundSubPublicModel
import anduin.telemetry.TelemetryEnvironment
import anduin.workflow.TemporalWorkflowService
import anduin.workflow.fundsub.simulator.*
import com.anduin.stargazer.service.utils.ZIOUtils

final case class FundSubSimulatorSetupDemoOrdersActivitiesImpl(
  fundSubSimulatorService: FundSubSimulatorService,
  fundDataSimulatorService: FundDataSimulatorService,
  fundSubViewService: FundSubViewService,
  tracingEnvironment: TelemetryEnvironment.Tracing
)(
  using val temporalWorkflowService: TemporalWorkflowService
) extends FundSubSimulatorSetupDemoOrdersActivities {

  override def setupDemoSignature(params: SetupDemoSignatureParams): EmptyResponse = {
    temporalWorkflowService.executeTask(
      fundSubSimulatorService.setupDemoSignature(params.actor).as(EmptyResponse()),
      "setupDemoUsers"
    )
  }

  override def createDemoLps(params: CreateDemoLpsParams): CreateDemoLpsResponse = {
    val task = for {
      fundData <- ZIO.getOrFail(params.fundData)
      counterRef <- zio.Ref.make(params.currentProgress)
      createdLps <- ZIOUtils.foreachParN(4)(fundData.demoOrders) { order =>
        for {
          createdLpId <- fundSubSimulatorService
            .createDemoLpInternal(
              actor = params.actor,
              fundSubId = params.fundSubId,
              fundData = fundData,
              order = order,
              customDataColumnIds = params.customDataColumnIds,
              shouldDisableSoftReview = params.shouldDisableSoftReview
            )
            .map(_.createdLpId)
          counter <- counterRef.updateAndGet(_ + 1)
          _ <- fundSubSimulatorService.updateSimulatorProgressTracker(
            fundSubId = params.mainFundSubId,
            progressStatus = FundSubSimulatorService.ProgressStatus.Processing(
              100 * counter / params.numTotalOrders
            )
          )
        } yield CreatedDemoLps(
          fundSubLpId = createdLpId,
          lpInvestmentEntityName = order.firmName,
          order.isMainLpDemo,
          order.isAdditionalLpDemo
        )
      }
    } yield CreateDemoLpsResponse(createdLps)

    temporalWorkflowService.executeTask(
      task.provideEnvironment(tracingEnvironment.environment),
      "createDemoLps"
    )
  }

  override def initializeRequestChangesComment(params: InitializeRequestChangesCommentParams): EmptyResponse = {
    temporalWorkflowService.executeTask(
      fundSubSimulatorService
        .initializeRequestChangesCommentData(
          params.actor,
          params.fundSubId,
          params.requestChangesCommentData
        )
        .as(EmptyResponse()),
      "initializeRequestChangesComment"
    )
  }

  override def resetDemoSignature(params: ResetDemoSignatureParams): EmptyResponse = {
    temporalWorkflowService.executeTask(
      fundSubSimulatorService
        .resetDemoSignature(params.actor)
        .as(EmptyResponse()),
      "resetDemoSignature"
    )
  }

  override def prepareGroupAndDashboard(params: PrepareGroupAndDashboardParams): EmptyResponse = {
    temporalWorkflowService.executeTask(
      fundSubSimulatorService
        .prepareAdditionalGroupAndDashboard(
          params.fundSubId,
          params.actor,
          params.customDataColumnIds
        )
        .as(EmptyResponse()),
      "prepareGroupAndDashboard"
    )
  }

  override def completeSetupDemoOrders(params: CompleteSetupDemoOrdersParams): EmptyResponse = {
    temporalWorkflowService.executeTask(
      fundSubSimulatorService
        .completeSetupDemoOrders(params.fundSubId)
        .as(EmptyResponse()),
      "completeSetupDemoOrders"
    )
  }

  override def setupDemoOrdersError(params: SetupDemoOrdersErrorParams): EmptyResponse = {
    temporalWorkflowService.executeTask(
      fundSubSimulatorService
        .onSetupDemoOrdersError(params.fundSubId)
        .as(EmptyResponse()),
      "completeSetupDemoOrders"
    )
  }

  override def setUpDemoFundData(params: SetupDemoFundDataParams): EmptyResponse = {
    temporalWorkflowService.executeTask(
      (for {
        firmId <- fundDataSimulatorService
          .setUpDemoFirm(
            params.actor,
            params.entityId,
            params.fundSubId,
            params.createdLps.map(createdLp =>
              FundDataSimulatorService.CreatedLpInfo(createdLp.fundSubLpId, createdLp.lpInvestmentEntityName)
            ),
            createdFundSubs = params.createdFundSubs.map(createdFundSub =>
              FundDataSimulatorService.CreatedFundSub(
                createdFundSub.fundSubId,
                createdFundSub.fundSubName,
                createdFundSub.sharableLinkId
              )
            ),
            createdDataRooms = params.createdDataRooms.map(createdDataRoom =>
              FundDataSimulatorService.CreatedDataRoom(
                createdDataRoom.dataRoomId,
                createdDataRoom.dataRoomName,
                createdDataRoom.linkId
              )
            )
          )
        _ <- FDBRecordDatabase
          .transact(FundSubModelStoreOperations.Production) { ops =>
            ops.updateFundSubPublicModel(params.fundSubId) { (model: FundSubPublicModel) =>
              model.copy(demoInfo = model.demoInfo.map(_.withDemoFundDataFirmIdOpt(firmId)))
            }
          }
      } yield ()).as(EmptyResponse()),
      "setUpDemoFundData"
    )
  }

  override def setUpMarketingDataRoom(params: SetupMarketingDataRoomParams): SetupMarketingDataRoomResponse = {
    temporalWorkflowService.executeTask(
      (for {
        resps <- fundSubSimulatorService.createMarketingDataRoom(
          emailAddress = params.actorEmail,
          entityId = params.entityId,
          fundSubLpId = params.fundSubLpId
        )
        _ <- FDBRecordDatabase
          .transact(FundSubModelStoreOperations.Production) { ops =>
            ops.updateFundSubPublicModel(params.fundSubLpId.parent) { (model: FundSubPublicModel) =>
              model.copy(demoInfo =
                model.demoInfo.map(_.withDemoMarketingDataRoomWorkflowIdOpt(resps.head.dataRoomWorkflowId))
              )
            }
          }
      } yield SetupMarketingDataRoomResponse(
        resps.map(resp => CreatedDemoDataRoom(resp.dataRoomWorkflowId, resp.dataRoomName, resp.shareableLinkId))
      )),
      "setUpMarketingDataRoom"
    )
  }

}
