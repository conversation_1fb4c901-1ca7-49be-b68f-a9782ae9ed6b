// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.simulator

import zio.temporal.{activityInterface, activityMethod}

@activityInterface
trait FundSubSimulatorSetupDemoOrdersActivities {

  @activityMethod
  def setupDemoSignature(params: SetupDemoSignatureParams): EmptyResponse

  @activityMethod
  def createDemoLps(params: CreateDemoLpsParams): CreateDemoLpsResponse

  @activityMethod
  def initializeRequestChangesComment(params: InitializeRequestChangesCommentParams): EmptyResponse

  @activityMethod
  def resetDemoSignature(params: ResetDemoSignatureParams): EmptyResponse

  @activityMethod
  def prepareGroupAndDashboard(params: PrepareGroupAndDashboardParams): EmptyResponse

  @activityMethod
  def completeSetupDemoOrders(params: CompleteSetupDemoOrdersParams): EmptyResponse

  @activityMethod
  def setUpDemoFundData(params: SetupDemoFundDataParams): EmptyResponse

  @activityMethod
  def setUpMarketingDataRoom(params: SetupMarketingDataRoomParams): SetupMarketingDataRoomResponse

  @activityMethod
  def setupDemoOrdersError(params: SetupDemoOrdersErrorParams): EmptyResponse

}
