// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.dataextract.service

import zio.Task

import anduin.fdb.record.FDBRecordDatabase
import anduin.fundsub.dataextract.database.{FundSubDataExtractLogStoreOperations, FundSubDataExtractLogStoreProvider}
import anduin.fundsub.dataextract.log.{FundSubDataExtractLogEventTypeProto, FundSubDataExtractLogRecord}
import anduin.fundsub.dataextract.{FundSubDataExtractLog, FundSubDataExtractLogType}
import anduin.id.fundsub.FundSubLpId

object FundSubDataExtractLogRepo {

  def create(
    lpId: FundSubLpId,
    logs: Seq[FundSubDataExtractLog]
  ): Task[Unit] = {

    val records = logs.map { log =>
      val eventType = toFundSubDataExtractLogEventTypeProto(log.eventType)
      FundSubDataExtractLogRecord(
        lpId = log.lpId,
        actor = log.actorOpt,
        createdAt = log.createdAt,
        fieldAlias = log.fieldAlias,
        eventType = eventType,
        oldValue = log.oldValue,
        oldValueFormated = log.oldValueFormated,
        newValue = log.newValue,
        newValueFormated = log.newValueFormated,
        formVersionId = log.formVersionIdOpt,
        dataExtractProjectItemId = log.dataExtractProjectItemIdOpt,
        submissionVersion = log.submissionVersion
      )
    }

    FDBRecordDatabase.transact(FundSubDataExtractLogStoreProvider.Production) { store =>
      FundSubDataExtractLogStoreOperations(store)
        .batchCreate(lpId = lpId, records = records)
    }
  }

  def getByLpId(lpId: FundSubLpId): Task[List[FundSubDataExtractLog]] = {
    FDBRecordDatabase
      .transact(FundSubDataExtractLogStoreProvider.Production) { store =>
        FundSubDataExtractLogStoreOperations(store)
          .getAllItems(lpId)
      }
      .map(_.map { item =>
        val eventType = fromFundSubDataExtractLogEventTypeProto(item.eventType)

        FundSubDataExtractLog(
          lpId = item.lpId,
          itemIndex = item.itemIndex,
          actorOpt = item.actor,
          createdAt = item.createdAt,
          fieldAlias = item.fieldAlias,
          eventType = eventType,
          oldValue = item.oldValue,
          oldValueFormated = item.oldValueFormated,
          newValue = item.newValue,
          newValueFormated = item.newValueFormated,
          formVersionIdOpt = item.formVersionId,
          dataExtractProjectItemIdOpt = item.dataExtractProjectItemId,
          submissionVersion = item.submissionVersion,
          repeatableIndexOpt = item.repeatableIndexOpt
        )
      })
  }

  private def toFundSubDataExtractLogEventTypeProto(eventType: FundSubDataExtractLogType) = {
    eventType match {
      // convert to FundSubDataExtractLogEventTypeProto
      case FundSubDataExtractLogType.FORM_UPDATE =>
        FundSubDataExtractLogEventTypeProto.FUND_SUB_DATA_EXTRACT_LOG_EVENT_TYPE_FORM_UPDATE
      case FundSubDataExtractLogType.OCR_DATA_SUBMISSION =>
        FundSubDataExtractLogEventTypeProto.FUND_SUB_DATA_EXTRACT_LOG_EVENT_TYPE_OCR_DATA_SUBMISSION
      case FundSubDataExtractLogType.USER_MODIFICATION =>
        FundSubDataExtractLogEventTypeProto.FUND_SUB_DATA_EXTRACT_LOG_EVENT_TYPE_USER_MODIFICATION
    }
  }

  private def fromFundSubDataExtractLogEventTypeProto(eventType: FundSubDataExtractLogEventTypeProto) = {
    eventType match {
      case FundSubDataExtractLogEventTypeProto.FUND_SUB_DATA_EXTRACT_LOG_EVENT_TYPE_OCR_DATA_SUBMISSION =>
        FundSubDataExtractLogType.OCR_DATA_SUBMISSION
      case FundSubDataExtractLogEventTypeProto.FUND_SUB_DATA_EXTRACT_LOG_EVENT_TYPE_FORM_UPDATE =>
        FundSubDataExtractLogType.FORM_UPDATE
      case FundSubDataExtractLogEventTypeProto.FUND_SUB_DATA_EXTRACT_LOG_EVENT_TYPE_USER_MODIFICATION =>
        FundSubDataExtractLogType.USER_MODIFICATION
      case _ => FundSubDataExtractLogType.USER_MODIFICATION
    }
  }

}
