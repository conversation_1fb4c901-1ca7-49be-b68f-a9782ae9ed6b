// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.simulator

import java.util.UUID

import io.circe.syntax.*
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.fdb.record.FDBRecordDatabase
import anduin.forms.integration.*
import anduin.forms.storage.integration.FormIntegrationStoreOperations
import anduin.fundsub.constants.DemoData
import anduin.fundsub.dashboard.{ColumnConverter, FundSubDashboardService}
import anduin.fundsub.dataexport.FundSubExportService
import anduin.fundsub.datalakeingestion.FundSubDataLakeIngestionService
import anduin.fundsub.endpoint.DemoFormType
import anduin.fundsub.endpoint.customdata.GenerateCustomDataColumnParams
import anduin.fundsub.endpoint.dashboard.v2.{UpdateDashboardColumnParams, UpdateDashboardConfigParams}
import anduin.fundsub.endpoint.dataexport.{
  FundSubExportTemplateInfoDetail,
  GenerateDefaultExportTemplateParams,
  LoadExportTemplateFromFileParams,
  SaveExportTemplateInfoParams
}
import anduin.fundsub.endpoint.lp.FormReferenceInfo
import anduin.fundsub.endpoint.operation.*
import anduin.fundsub.investmentfund.FundSubInvestmentFundSharedUtils
import anduin.fundsub.link.FundSubCreateFormLink
import anduin.fundsub.models.{FundSubModelStoreOperations, FundSubSgwModelUtils}
import anduin.fundsub.service.FundSubGreylinDataService
import anduin.fundsub.simulator.SimulatorData.{AdditionalSetup, DemoExportTemplateConfig}
import anduin.fundsub.simulator.model.{DemoFormData, DemoFundData}
import anduin.fundsub.whitelabel.{FundSubWhiteLabelOperations, FundSubWhiteLabelStore}
import anduin.id.fundsub.{FundSubAdminRestrictedId, FundSubId, FundSubReportingId}
import anduin.id.link.ProtectedLinkId
import anduin.link.ProtectedLinkService
import anduin.model.codec.ProtoCodecs.given
import anduin.model.common.user.UserId
import anduin.model.document.DocumentStorageId
import anduin.model.id.{DocumentStorageIdFactory, FolderId, FundSubExportTemplateIdFactory}
import anduin.portaluser.ExecutiveAdmin
import anduin.protobuf.DocumentStorageIdMessage
import anduin.protobuf.external.squants.CurrencyMessage
import anduin.protobuf.fundsub.exporter.FundSubExportTemplate
import anduin.protobuf.fundsub.models.*
import anduin.protobuf.fundsub.models.customdata.item.*
import anduin.protobuf.fundsub.whitelabel.FundSubWhiteLabelModel
import anduin.protobuf.fundsub.{DemoInfo, FeatureSwitch, FundSubEvent, FundSubProtectedLinkModel}
import anduin.service.GeneralServiceException
import anduin.storageservice.common.FileContentOrigin
import anduin.storageservice.s3.S3Service
import anduin.util.FilenameUtils
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.fundsub.free.module.ManageFundSubAdminM
import com.anduin.stargazer.service.fundsub.operation.{FundSubOperationService, FundSubOperationUtils}
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOUtils

final case class FundSubSimulatorOperationService(
  adminModule: ManageFundSubAdminM,
  backendConfig: GondorBackendConfig,
  executiveAdmin: ExecutiveAdmin,
  fundSubDashboardService: FundSubDashboardService,
  fundSubExportService: FundSubExportService,
  fundSubOperationService: FundSubOperationService,
  natsNotificationService: NatsNotificationService,
  protectedLinkService: ProtectedLinkService,
  s3Service: S3Service
)(
  using userProfileService: UserProfileService,
  fileService: FileService,
  fundSubDataLakeIngestionService: FundSubDataLakeIngestionService,
  fundSubGreylinDataService: FundSubGreylinDataService
) {

  private val FundSubSimulatorFolder = "fundsub-simulator"
  private val ResourcesBucket = backendConfig.aws.S3.resourcesBucket

  def getFeatureSwitch(demoFormType: DemoFormType): FeatureSwitch = {
    FeatureSwitch(
      allowMultiStepInSubscriptionReview = true,
      enableSupportingDocReview = true,
      formCommentSwitch = true,
      disableSubmissionInstruction = true,
      enabledAddAdditionalTaxForms = true,
      enableAutoPrefill = true,
      enableFundPermission = true,
      enableImportData = true,
      enableAmlKycCommenting = true,
      enableCommentMentioning = true,
      enableCommentAssignment = true,
      enableImportFromFundData = demoFormType == DemoFormType.UsDemoForm,
      enableSideLetter = demoFormType == DemoFormType.UsDemoForm || demoFormType == DemoFormType.LuxDemoForm
    )
  }

  def disableFundEmails(fundSubId: FundSubId, actor: UserId): Task[Unit] = {
    fundSubOperationService.updateFundSubEmailConfig(
      params = UpdateFundSubEmailConfigParams(
        fundSubId = fundSubId,
        emailTemplates = Seq.empty,
        disabledEmails = FundSubEvent.values.toSet,
        customSenderEmailAddress = None,
        customEmailReply = None,
        emailReplyToSenderConfig = None,
        customEmailCcConfig = None,
        senderCcConfig = None
      ),
      actor = actor
    )
  }

  def setUpReferenceDocs(
    fundSubId: FundSubId,
    userId: UserId,
    refDocs: Seq[String]
  ): Task[Unit] = {
    for {
      files <- ZIO.foreach(refDocs) { refDoc =>
        fileService.uploadFile(
          FolderId.channelSystemFolderId(fundSubId),
          refDoc,
          FileContentOrigin.FromStorageId(
            DocumentStorageId(s"$FundSubSimulatorFolder/fundsub/$refDoc"),
            ResourcesBucket
          ),
          userId
        )
      }
      _ <- adminModule.uploadRefDoc(fundSubId, files)
    } yield ()
  }

  def setUpProtectedLink(fundSubId: FundSubId): Task[Unit] = {
    for {
      (createdLink, _) <- protectedLinkService.create(
        Some("fundraise"),
        None,
        Set.empty,
        FundSubCreateFormLink(fundSubId).asMessage.asJson.noSpaces,
        None
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId)) {
          (restrictedModel: FundSubAdminRestrictedModel) =>
            restrictedModel.copy(protectLink = Some(FundSubProtectedLinkModel(linkId = Some(createdLink))))
        }
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubAdminRestrictedModel(fundSubId)(natsNotificationService)
    } yield ()
  }

  def getProtectedLinkId(fundSubId: FundSubId): Task[ProtectedLinkId] = {
    for {
      linkIdOpt <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId)).map(_.protectLink.flatMap(_.linkId))
      }
      linkId <- ZIOUtils.fromOption(
        linkIdOpt,
        GeneralServiceException("Failed to get protected link id for fund sub")
      )
    } yield linkId
  }

  def setUpWhiteLabel(fundSubId: FundSubId, whiteLabel: AdditionalSetup.WhiteLabel): Task[Unit] = {
    for {
      fundSquareLogoStorageIdOpt <- whiteLabel.fundSquareLogoOpt
        .map { logo =>
          uploadLogoFile(fundSubId, logo).map(Some(_))
        }
        .getOrElse(ZIO.attempt(None))
      fundLongLogoStorageIdOpt <- whiteLabel.fundLongLogoOpt
        .map { logo =>
          uploadLogoFile(fundSubId, logo).map(Some(_))
        }
        .getOrElse(ZIO.attempt(None))
      providerSquareLogoStorageIdOpt <- whiteLabel.providerLogoOpt
        .map { providerLogo =>
          uploadLogoFile(fundSubId, providerLogo).map(Some(_))
        }
        .getOrElse(ZIO.attempt(None))
      providerLongLogoStorageIdOpt <- whiteLabel.providerLongLogoOpt
        .map { providerLongLogo =>
          uploadLogoFile(fundSubId, providerLongLogo).map(Some(_))
        }
        .getOrElse(ZIO.attempt(None))
      _ <- FDBRecordDatabase.transact(FundSubWhiteLabelStore.Production) { store =>
        FundSubWhiteLabelOperations(store).add(
          FundSubWhiteLabelModel(
            fundSubId = fundSubId,
            isEnabled = true,
            logoStorageId = fundSquareLogoStorageIdOpt,
            longLogoStorageId = fundLongLogoStorageIdOpt,
            enableProviderBranding = providerSquareLogoStorageIdOpt.nonEmpty || providerLongLogoStorageIdOpt.nonEmpty,
            providerLogo = providerSquareLogoStorageIdOpt,
            providerLongLogo = providerLongLogoStorageIdOpt,
            customValues = whiteLabel.customValues
          )
        )
      }
    } yield ()
  }

  private def uploadLogoFile(fundSubId: FundSubId, logoFileName: String): Task[DocumentStorageIdMessage] = {
    val finalFileName = FilenameUtils.appendSuffix(logoFileName, UUID.randomUUID.toString)
    val storageId = DocumentStorageIdFactory.generate(fundSubId, finalFileName)
    for {
      _ <- s3Service.copyObject(
        sourceStorageId = DocumentStorageId(s"$FundSubSimulatorFolder/fundsub/whitelabels/$logoFileName"),
        sourceBucket = ResourcesBucket,
        destStorageId = storageId,
        destBucket = s3Service.s3Config.publicBucket,
        contentType = "image/png"
      )
    } yield DocumentStorageIdMessage(storageId.id)
  }

  def initExportTemplate(
    exportConfig: DemoExportTemplateConfig,
    taxForms: Map[String, Seq[DemoFormData]],
    fundId: FundSubId,
    demoFormType: DemoFormType
  ): Task[Unit] = {
    for {
      adminUser <- executiveAdmin.userId
      templateFileId <- fileService.uploadFile(
        parentFolderId = FolderId.channelSystemFolderId(fundId),
        fileName = exportConfig.exportFileName,
        FileContentOrigin.FromStorageId(
          DocumentStorageId(s"$FundSubSimulatorFolder/${exportConfig.exportPublicFilePath}"),
          ResourcesBucket
        ),
        adminUser
      )
      template <- fundSubExportService
        .loadExportTemplateFromFile(
          LoadExportTemplateFromFileParams(
            fileId = templateFileId,
            sheet = 0,
            startRow = exportConfig.startRow,
            startCol = 0
          ),
          adminUser
        )
        .map(_.exportTemplate)
      defaultTemplates <- fundSubExportService
        .generateDefaultExportTemplate(
          GenerateDefaultExportTemplateParams(
            fundSubId = fundId,
            taxFormInfoSeq = taxForms.flatMap { case (groupName, forms) =>
              forms.map(form =>
                FormReferenceInfo(
                  groupName = groupName,
                  name = form.formName,
                  formId = Right(form.formVersionIds.last)
                )
              )
            }.toSeq,
            includeMainForm = true
          ),
          adminUser
        )
        .map(_.exportTemplates)
      _ <- fundSubExportService.saveExportTemplateInfo(
        SaveExportTemplateInfoParams(
          FundSubExportTemplateInfoDetail(
            fundId,
            isEnabled = demoFormType != DemoFormType.LightLogicDemoForm,
            defaultTemplates.map(_.withInUsed(true)) ++ exportConfig.exportTemplateNames.map { templateName =>
              val templateId = FundSubExportTemplateIdFactory.unsafeRandomId(fundId)
              FundSubExportTemplate(
                fieldConfigs = template.fieldConfigs,
                templateId = templateId,
                inUsed = true,
                startCol = template.startCol,
                startRow = template.startRow,
                templateFileOpt = Some(templateFileId),
                templateName = templateName,
                shouldExportOptionLabel = Some(true)
              )
            },
            isPdfTemplateEnabled = false
          )
        ),
        adminUser
      )
    } yield ()
  }

  def getInvestmentFunds(
    fundData: DemoFundData,
    demoFormType: DemoFormType
  ): Task[Seq[InvestmentFundModel]] = {
    for {
      integrationOpt <- fundData.formData
        .flatMap(_.formVersionIds.lastOption)
        .fold {
          ZIO.succeed(Option.empty[FormVersionIntegrationModel])
        } { formVersionId =>
          FDBRecordDatabase.transact(FormIntegrationStoreOperations.Production) { ops =>
            ops.getOpt(formVersionId)
          }
        }
      subFundConfigs = integrationOpt.flatMap(_.fundEnvironment).fold(Seq.empty[SubFundConfig]) { configData =>
        configData.fundConfig match {
          case FundConfigData.Empty           => Seq.empty
          case WithoutFundSelection(subFunds) => subFunds
          case WithFundSelection(selections)  => selections.flatMap(_.subFunds)
        }
      }
    } yield {
      if (fundData.capitalId.nonEmpty || subFundConfigs.isEmpty) {
        Seq(
          InvestmentFundModel(
            amountFieldId = fundData.capitalId,
            currency = CurrencyMessage.USD,
            name = fundData.fundName,
            amountColumnName = "Amount",
            estimatedAmountColumnName = "Estimated amount",
            submittedAmountColumnName = "Capital amount",
            acceptedAmountColumnName = "Accepted amount"
          )
        )
      } else {
        demoFormType match {
          case DemoFormType.OpenEndedDemoForm =>
            subFundConfigs.map { config =>
              InvestmentFundModel(
                amountFieldId = config.amountField,
                currency = CurrencyMessage.fromName(config.currency.code).getOrElse(CurrencyMessage.USD),
                name = config.displayName,
                amountColumnName = config.displayName,
                estimatedAmountColumnName = s"Estimated ${config.displayName}",
                submittedAmountColumnName = s"Capital ${config.displayName}",
                acceptedAmountColumnName = s"Accepted ${config.displayName}"
              )
            }
          case _ =>
            val subFundConfigCount = subFundConfigs.size
            subFundConfigs.map { config =>
              FundSubInvestmentFundSharedUtils.constructInvestmentFund(config, subFundConfigCount)
            }
        }
      }
    }
  }

  def updateFundSubDemoInfo(
    mainFundId: FundSubId,
    additionalFundIds: Seq[FundSubId]
  ): Task[Unit] = {
    for {
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
        _.updateFundSubPublicModel(mainFundId) { model =>
          model.withDemoInfo(
            DemoInfo(
              mainDemoFundSubId = mainFundId,
              additionalDemoFundSubIds = additionalFundIds
            )
          )
        }
      }
      _ <- ZIOUtils.foreachParN(2)(additionalFundIds) { additionalFundId =>
        FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
          _.updateFundSubPublicModel(additionalFundId) { model =>
            model.withDemoInfo(DemoInfo(mainDemoFundSubId = mainFundId))
          }
        }
      }
    } yield ()
  }

  def syncFundData(
    fundSubId: FundSubId,
    params: CreateFundSubParams,
    fundAdminUserIds: Seq[UserId]
  ): Task[Unit] = {
    for {
      fsRestrictedModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
      }
      _ <- FundSubOperationUtils.addNewFundDataToDataLake(
        params = params,
        fundSubId = fundSubId,
        investmentFunds = fsRestrictedModel.investmentFunds,
        inactiveNotificationSettingOpt = fsRestrictedModel.inactiveNotificationSetting,
        admins = fundAdminUserIds
      )
      _ <- FundSubOperationUtils.addNewFundDataToGreylin(
        params = params,
        fundSubId = fundSubId,
        investmentFunds = fsRestrictedModel.investmentFunds,
        inactiveNotificationSettingOpt = fsRestrictedModel.inactiveNotificationSetting,
        admins = fundAdminUserIds
      )
    } yield ()
  }

  def prepareMasterDashboardData(
    fundSubId: FundSubId,
    enableStandardDashboard: Boolean,
    demoFormType: DemoFormType,
    actor: UserId
  ): Task[Unit] = {
    for {
      adminUser <- executiveAdmin.userId
      _ <- fundSubOperationService.saveDashboardConfig(
        params = SaveDashboardConfigParams(
          fundSubId,
          DashboardConfig(
            enableAdvancedDashboard = true,
            enableStandardDashboard = enableStandardDashboard,
            syncDataEnabled = true,
            formFieldsConfigs = SimulatorData.getDashboardFormFields(demoFormType)
          )
        ),
        adminUser
      )
      masterDashboardId <- fundSubDashboardService.getMasterDashboardId(fundSubId)
      masterDashboardInfo <- fundSubDashboardService.getDashboardGeneralInfo(
        fundSubId,
        Some(masterDashboardId),
        actor
      )
      noteColumnConfigs <- ZIO.foreach(DemoData.NoteColumns) { nodeColumnData =>
        fundSubDashboardService.customDataService
          .generateCustomDataColumn(
            GenerateCustomDataColumnParams(
              fundId = fundSubId,
              name = nodeColumnData._1,
              description = nodeColumnData._2,
              dataType = StringValue()
            ),
            actor
          )
          .map(_.customDataColumn)
      }
      singleSelectColumnConfigs <- ZIO.foreach(DemoData.SingleSelectColumns) { singleSelectColumnData =>
        fundSubDashboardService.customDataService
          .generateCustomDataColumn(
            GenerateCustomDataColumnParams(
              fundId = fundSubId,
              name = singleSelectColumnData._1,
              description = singleSelectColumnData._2,
              dataType = SingleStringValue(
                valueWithColor = DemoData.SingleSelect1Options.map { value =>
                  StringWithColor(
                    content = value
                  )
                }
              )
            ),
            actor
          )
          .map(_.customDataColumn)
      }
      multipleSelectColumnConfigs <- ZIO.foreach(DemoData.MultipleSelectColumns) { multipleSelectColumnData =>
        fundSubDashboardService.customDataService
          .generateCustomDataColumn(
            GenerateCustomDataColumnParams(
              fundId = fundSubId,
              name = multipleSelectColumnData._1,
              description = multipleSelectColumnData._2,
              dataType = MultipleStringValue(
                valueWithColor = DemoData.MultipleSelect1Options.map { value =>
                  StringWithColor(
                    content = value
                  )
                }
              )
            ),
            actor
          )
          .map(_.customDataColumn)
      }
      dateTimeColumnConfigs <- ZIO.foreach(DemoData.DateTimeColumns) { dateTimeColumnData =>
        fundSubDashboardService.customDataService
          .generateCustomDataColumn(
            GenerateCustomDataColumnParams(
              fundId = fundSubId,
              name = dateTimeColumnData._1,
              description = dateTimeColumnData._2,
              dataType = DateTimeValue()
            ),
            actor
          )
          .map(_.customDataColumn)
      }
      customDataColumns =
        noteColumnConfigs ++ singleSelectColumnConfigs ++ multipleSelectColumnConfigs ++ dateTimeColumnConfigs
      _ <- fundSubDashboardService.updateDashboardColumn(
        UpdateDashboardColumnParams(
          fundSubId,
          Some(masterDashboardId),
          None,
          availableColumnIds = masterDashboardInfo.availableTypedColumns.map(_.id) ++
            customDataColumns.map(ColumnConverter.customDataConfigToColumnId),
          toAddCustomColumnConfigs = customDataColumns,
          isPrivateView = false
        ),
        actor
      )
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(fundSubId)
      }
      visibleColumnIds = SimulatorData.getDashboardVisibleColumnIds(
        demoFormType = demoFormType,
        investmentFunds = adminResModel.investmentFunds,
        customDataColumnIds = customDataColumns.map(ColumnConverter.customDataConfigToColumnId),
        formDataColumnIds = masterDashboardInfo.formFieldConfigs.map(ColumnConverter.formFieldConfigToColumnId)
      )
      _ <- fundSubDashboardService.updateDashboardConfig(
        UpdateDashboardConfigParams(
          fundSubId,
          Some(masterDashboardId),
          visibleColumnIds = visibleColumnIds,
          pageSize = masterDashboardInfo.pageSize
        ),
        actor
      )
    } yield ()
  }

  def updateReportingModelForOpenEndedFund(fundSubId: FundSubId): Task[Unit] = {
    FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
      for {
        adminResModel <- ops.getFundSubAdminRestrictedModel(fundSubId)
        withdrawalFundIdOpt = adminResModel.investmentFunds.find(_.name == "Withdrawal Amount").map(_.fundId)
        reportingModel = FundSubReportingModel(
          fundSubReportingId = FundSubReportingId(fundSubId),
          excludedInvestmentFunds = withdrawalFundIdOpt.toSet
        )
        _ <- ops.updateOrCreateFundSubReportingModel(fundSubId, reportingModel)
      } yield ()
    }
  }

}
