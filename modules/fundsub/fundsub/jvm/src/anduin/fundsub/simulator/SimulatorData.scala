// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.simulator

import anduin.forms.Form
import anduin.fundsub.constants.DemoData
import anduin.fundsub.endpoint.{DemoEnvironment, DemoFormType}
import anduin.fundsub.endpoint.admin.FundAdminInfo
import anduin.fundsub.endpoint.group.{FundSubGroupMemberInfo, FundSubGroupRoleType}
import anduin.fundsub.utils.FundSubWhiteLabelUtils.CustomValue
import anduin.id.fundsub.FundSubId
import anduin.model.user.FullName
import anduin.protobuf.external.squants.MoneyMessage
import anduin.protobuf.fundsub.{FundSubAdminRole, FundSubSupportingContactInfo, FundSubSupportingContacts}
import anduin.util.EmailAddressConstants
import com.anduin.stargazer.utils.EmailAddressUtils
import anduin.dashboard.model.DashboardColumnData.*
import anduin.dashboard.model.*
import anduin.fundsub.simulator.model.*
import anduin.protobuf.fundsub.models.InvestmentFundModel

object SimulatorData {

  final case class DemoExportTemplateConfig(
    exportFileName: String,
    exportPublicFilePath: String,
    exportTemplateNames: Seq[String],
    startRow: Int
  )

  final case class DemoFundDataSetupConfig(
    fundData: DemoFundData,
    fundAdmins: Seq[FundAdminInfo],
    taxForms: Map[String, Seq[DemoFormData]],
    refDocs: Seq[String],
    targetCapital: MoneyMessage,
    daysUntilClosing: Long,
    exportTemplateConfigOpt: Option[DemoExportTemplateConfig],
    shouldSetupDemoOrders: Boolean = true,
    shouldSendVerificationEmail: Boolean = true,
    enableLpProfile: Boolean = false,
    additionalFundSetupConfigs: Seq[DemoFundDataSetupConfig] =
      Seq.empty, // For setting up additional funds beside main fund, when the demo required more than 1 fund, eg FDM
    shouldCreateDemoInvestorAccess: Boolean = false
  )

  val guestName: FullName = FullName("Guest", "Account")
  val guestPassword: String = "anduin1808"
  val guestEmail: String = "<EMAIL>"
  val orgName: String = "Anduin"

  val supportingContact: FundSubSupportingContacts = FundSubSupportingContacts(
    "Anduin Transactions",
    Seq(
      FundSubSupportingContactInfo(
        "Technical support",
        EmailAddressConstants.SupportEmailAddress,
        "+1-877-665-5255"
      )
    )
  )

  val blacklistedDomains: Seq[String] = EmailAddressUtils.PopularEmailDomains ++ Seq(
    "carta.com",
    "wsgr.com"
  )

  private val DefaultFundAdmins: Seq[FundAdminInfo] = Seq(
    FundAdminInfo(
      email = "<EMAIL>",
      firstName = "Duncan",
      lastName = "Berge",
      role = FundSubAdminRole.FundSubAdmin
    ),
    FundAdminInfo(
      email = "<EMAIL>",
      firstName = "Madison",
      lastName = "Brown",
      role = FundSubAdminRole.FundSubAdmin
    ),
    FundAdminInfo(
      email = "<EMAIL>",
      firstName = "George",
      lastName = "Walsh",
      role = FundSubAdminRole.FundSubAdmin
    ),
    FundAdminInfo(
      email = "<EMAIL>",
      firstName = "Jonathin",
      lastName = "Sudin",
      role = FundSubAdminRole.FundSubExternalAdmin
    ),
    FundAdminInfo(
      email = "<EMAIL>",
      firstName = "Thomas",
      lastName = "Miller",
      role = FundSubAdminRole.FundSubExternalAdmin
    ),
    FundAdminInfo(
      email = "<EMAIL>",
      firstName = "Noah",
      lastName = "Wilson",
      role = FundSubAdminRole.FundSubCounsel
    ),
    FundAdminInfo(
      email = "<EMAIL>",
      firstName = "Anduin",
      lastName = "Support",
      role = FundSubAdminRole.AnduinSupport
    ),
    FundAdminInfo(
      email = guestEmail,
      firstName = guestName.firstName,
      lastName = guestName.lastName,
      role = FundSubAdminRole.FundSubAdmin
    )
  )

  private val TaxFormsMap = Map(
    "W-9 Form" -> Seq(DemoData.demoW9TaxNewForm),
    "The Appropriate Form W-8" -> Seq(DemoData.demoW8BENTaxForm, DemoData.demoW8BENETaxForm)
  )

  private val SampleFundConfig = DemoFundDataSetupConfig(
    fundData = DemoData.SampleFundData,
    fundAdmins = DefaultFundAdmins,
    taxForms = TaxFormsMap,
    refDocs = Seq("LPA.pdf", "PPM.pdf"),
    targetCapital = MoneyMessage(value = "*********"),
    daysUntilClosing = 100,
    exportTemplateConfigOpt = Some(
      DemoExportTemplateConfig(
        exportFileName = "Fund-Sub-Simulator-Export-Template.xlsx",
        exportPublicFilePath = "fundsub/fund-sub-simulator-export-template.xlsx",
        exportTemplateNames = Seq("Anduin general info template"),
        startRow = 1
      )
    )
  )

  private val InvestorAccessFundConfig: DemoFundDataSetupConfig = SampleFundConfig.copy(
    fundData = DemoData.InvestorAccessFund,
    targetCapital = MoneyMessage(value = "*********"),
    exportTemplateConfigOpt = None,
    enableLpProfile = true,
    shouldCreateDemoInvestorAccess = true
  )

  private val AdditionalFundForFundDataConfig: DemoFundDataSetupConfig = SampleFundConfig.copy(
    fundData = DemoData.AdditionalFundForFundData,
    exportTemplateConfigOpt = None,
    enableLpProfile = true
  )

  val FlexibleFundConfig: DemoFundDataSetupConfig = SampleFundConfig.copy(
    fundData = DemoData.UsCaymanFund,
    enableLpProfile = true,
    additionalFundSetupConfigs = Seq(InvestorAccessFundConfig, AdditionalFundForFundDataConfig)
  )

  private val LuxFormFundConfig: DemoFundDataSetupConfig = SampleFundConfig.copy(
    fundData = DemoData.LuxFund
  )

  private val StandardOfferingFundConfig: DemoFundDataSetupConfig = SampleFundConfig.copy(
    fundData = DemoData.StandardOfferingFund
  )

  private val LpTransferFundConfig: DemoFundDataSetupConfig = SampleFundConfig.copy(
    fundData = DemoData.LpTransferFund,
    targetCapital = MoneyMessage(value = "*********")
  )

  private val MasterSideLetterFundConfig: DemoFundDataSetupConfig = SampleFundConfig.copy(
    fundData = DemoData.MasterSideLetterFund,
    targetCapital = MoneyMessage(value = "*********")
  )

  private val OpenEndedFundConfig: DemoFundDataSetupConfig = SampleFundConfig.copy(
    fundData = DemoData.OpenEndedFund,
    targetCapital = MoneyMessage(value = "50000000")
  )

  private val DataExtractFundConfig: DemoFundDataSetupConfig = SampleFundConfig.copy(
    fundData = DemoData.DataExtract,
    enableLpProfile = true,
    taxForms = Map.empty
  )

  def getSetupConfig(demoFormType: DemoFormType): DemoFundDataSetupConfig = {
    demoFormType match {
      case DemoFormType.UsDemoForm               => FlexibleFundConfig
      case DemoFormType.LuxDemoForm              => LuxFormFundConfig
      case DemoFormType.LightLogicDemoForm       => StandardOfferingFundConfig
      case DemoFormType.LpTransferDemoForm       => LpTransferFundConfig
      case DemoFormType.MasterSideLetterDemoForm => MasterSideLetterFundConfig
      case DemoFormType.OpenEndedDemoForm        => OpenEndedFundConfig
      case DemoFormType.DataExtractFundForm      => DataExtractFundConfig
    }
  }

  def getDashboardVisibleColumnIds(
    demoFormType: DemoFormType,
    investmentFunds: Seq[InvestmentFundModel],
    customDataColumnIds: Seq[String],
    formDataColumnIds: Seq[String]
  ): Seq[String] = {
    demoFormType match {
      case DemoFormType.MasterSideLetterDemoForm =>
        Seq(
          contactColumn,
          lastActivityColumn,
          subscriptionDocumentsColumn,
          tagColumn,
          closeColumn,
          referenceDocumentColumn
        ).map(_.id)

      case DemoFormType.LpTransferDemoForm =>
        val estimatedAmountColumnOpt = investmentFunds.headOption.map { investmentFund =>
          expectedAmountColumn(investmentFund.fundId, investmentFund.estimatedAmountColumnName)
        }
        val columns = Seq(
          contactColumn,
          lastActivityColumn,
          subscriptionDocumentsColumn
        ) ++ estimatedAmountColumnOpt ++ Seq(
          tagColumn,
          closeColumn,
          referenceDocumentColumn
        )
        columns.map(_.id)

      case DemoFormType.OpenEndedDemoForm =>
        val amountColumns = investmentFunds.map { investmentFund =>
          generalAmountColumn(investmentFund.fundId, investmentFund.amountColumnName)
        }
        Seq(contactColumn.id) ++ formDataColumnIds ++
          Seq(
            closeColumn,
            subscriptionDocumentsColumn,
            requestedSignaturesColumn
          ).map(_.id) ++ amountColumns.map(_.id)

      case DemoFormType.DataExtractFundForm =>
        (Seq(
          contactColumn,
          closeColumn,
          lastActivityColumn,
          subscriptionDocumentsColumn,
          dataExtractionColumn,
          requestedDocumentsColumn,
          requestedSignaturesColumn,
          tagColumn,
          referenceDocumentColumn
        ) ++
          investmentFunds.flatMap(fundAmountColumns)).map(_.id)

      case _ =>
        val columns = Seq(contactColumn, closeColumn) ++
          Option.when(demoFormType == DemoFormType.UsDemoForm)(clientColumn) ++
          Seq(subscriptionDocumentsColumn, requestedDocumentsColumn) ++
          Option.when(demoFormType == DemoFormType.UsDemoForm)(sideLetterColumn) ++
          investmentFunds.flatMap(fundAmountColumns)
        columns.map(_.id) ++ customDataColumnIds ++ formDataColumnIds
    }
  }

  val businessDashboardFixedColumn: Seq[DashboardColumn] = Seq(
    contactColumn,
    closeColumn,
    subscriptionDocumentsColumn
  )

  def getDashboardFormFields(demoFormType: DemoFormType): Seq[FormFieldConfig] = {
    demoFormType match {
      case DemoFormType.UsDemoForm | DemoFormType.LuxDemoForm | DemoFormType.MasterSideLetterDemoForm |
          DemoFormType.DataExtractFundForm =>
        Seq(
          FormFieldConfig(
            namespace = Some(Form.DefaultNamespace),
            alias = "entityinvestortype",
            valueType = FormFieldValueType.StringType,
            valueFormat = TextFormat(),
            title = "Investor type",
            description = "",
            useLabel = false
          ),
          FormFieldConfig(
            namespace = Some(Form.DefaultNamespace),
            alias = "radio13",
            valueType = FormFieldValueType.BooleanType,
            valueFormat = TextFormat(),
            title = "US person",
            description = "",
            useLabel = false
          ),
          FormFieldConfig(
            namespace = Some(Form.DefaultNamespace),
            alias = "canadianinvestoryn",
            valueType = FormFieldValueType.BooleanType,
            valueFormat = TextFormat(),
            title = "Canadian subscriber",
            description = "",
            useLabel = false
          )
        )

      case DemoFormType.OpenEndedDemoForm =>
        Seq(
          FormFieldConfig(
            namespace = Some(Form.DefaultNamespace),
            alias = "capitalActivity",
            valueType = FormFieldValueType.StringType,
            valueFormat = TextFormat(),
            title = "Capital Activity",
            description = "",
            useLabel = true
          ),
          FormFieldConfig(
            namespace = Some(Form.DefaultNamespace),
            alias = "negative",
            valueType = FormFieldValueType.StringType,
            valueFormat = TextFormat(),
            title = "Capital Amount",
            description = "",
            useLabel = false
          ),
          FormFieldConfig(
            namespace = Some(Form.DefaultNamespace),
            alias = "amountwithdrawal_withdrawalformForDisplay",
            valueType = FormFieldValueType.FloatType,
            valueFormat = PercentageFormat(decimal = 2),
            title = "% Redemption",
            description = "",
            useLabel = false
          )
        )

      case DemoFormType.LightLogicDemoForm | DemoFormType.LpTransferDemoForm =>
        Seq.empty
    }
  }

  sealed trait AdditionalSetup derives CanEqual

  object AdditionalSetup {

    case object Dataroom extends AdditionalSetup

    case object StandardDashboard extends AdditionalSetup

    final case class WhiteLabel(
      fundSquareLogoOpt: Option[String] = None,
      fundLongLogoOpt: Option[String] = None,
      providerLogoOpt: Option[String] = None,
      providerLongLogoOpt: Option[String] = None,
      customValues: Map[String, String] = Map.empty
    ) extends AdditionalSetup

    final case class SubscriptionDocReviewConfig(
      shouldDisableSoftReview: Boolean = false
    ) extends AdditionalSetup

  }

  val DemoEnvironmentAdditionalSetup: Map[DemoEnvironment, Seq[AdditionalSetup]] = Map(
    DemoEnvironment.AnduinTransaction -> Seq(
      AdditionalSetup.Dataroom,
      AdditionalSetup.WhiteLabel(
        fundSquareLogoOpt = Some("default-logo.png"),
        fundLongLogoOpt = Some("default-logo-long.png")
      )
    ),
    DemoEnvironment.UltimusLeverpoint -> Seq(
      AdditionalSetup.Dataroom,
      AdditionalSetup.WhiteLabel(
        fundSquareLogoOpt = Some("ultimus-leverpoint-square-logo.png"),
        fundLongLogoOpt = Some("ultimus-leverpoint-long-logo.png")
      )
    ),
    DemoEnvironment.Standish -> Seq(
      AdditionalSetup.Dataroom,
      AdditionalSetup.WhiteLabel(
        fundSquareLogoOpt = Some("standish-square-logo.png"),
        fundLongLogoOpt = Some("standish-long-logo.png")
      )
    ),
    DemoEnvironment.Gen2 -> Seq(
      AdditionalSetup.Dataroom,
      AdditionalSetup.WhiteLabel(
        fundSquareLogoOpt = Some("gen2-square-logo.png"),
        fundLongLogoOpt = Some("gen2-long-logo.png")
      )
    ),
    DemoEnvironment.FIS -> Seq(
      AdditionalSetup.Dataroom,
      AdditionalSetup.WhiteLabel(
        fundSquareLogoOpt = Some("fis-square-logo.png"),
        fundLongLogoOpt = Some("fis-long-logo.png")
      )
    ),
    DemoEnvironment.Proskauer -> Seq(
      AdditionalSetup.Dataroom,
      AdditionalSetup.WhiteLabel(
        fundSquareLogoOpt = Some("proskauer-square-logo.png"),
        fundLongLogoOpt = Some("proskauer-long-logo.png")
      )
    ),
    DemoEnvironment.STB -> Seq(
      AdditionalSetup.Dataroom,
      AdditionalSetup.StandardDashboard,
      AdditionalSetup.WhiteLabel(
        providerLogoOpt = Some("stb-square-logo.png"),
        providerLongLogoOpt = Some("stb-long-logo.png"),
        customValues = Map(
          CustomValue.ProviderLongLogoDashboardHeightPx.value -> "65",
          CustomValue.ProviderLongLogoEmailFooterHeightPx.value -> "24",
          CustomValue.ProviderLogoText.value -> "Provided by"
        )
      ),
      AdditionalSetup.SubscriptionDocReviewConfig(
        shouldDisableSoftReview = true
      )
    ),
    DemoEnvironment.Gunder -> Seq(
      AdditionalSetup.Dataroom,
      AdditionalSetup.WhiteLabel(
        fundSquareLogoOpt = Some("gunder-square-logo.png"),
        fundLongLogoOpt = Some("gunder-long-logo.png")
      )
    ),
    DemoEnvironment.Lionpoint -> Seq(
      AdditionalSetup.Dataroom,
      AdditionalSetup.WhiteLabel(
        fundSquareLogoOpt = Some("lionpoint-square-logo.jpeg"),
        fundLongLogoOpt = Some("lionpoint-long-logo.png")
      )
    ),
    DemoEnvironment.RopesGray -> Seq(
      AdditionalSetup.Dataroom,
      AdditionalSetup.WhiteLabel(
        fundSquareLogoOpt = Some("ropesgray-square-logo.png"),
        fundLongLogoOpt = Some("ropesgray-long-logo.png")
      )
    ),
    DemoEnvironment.Latham -> Seq(
      AdditionalSetup.Dataroom,
      AdditionalSetup.WhiteLabel(
        fundSquareLogoOpt = Some("latham-square-logo.png"),
        fundLongLogoOpt = Some("latham-long-logo.png"),
        customValues = Map(
          CustomValue.LongLogoHeightInDashboard.value -> "20"
        )
      )
    ),
    DemoEnvironment.Allvue -> Seq(
      AdditionalSetup.Dataroom,
      AdditionalSetup.WhiteLabel(
        fundSquareLogoOpt = Some("allvue-square-logo.png"),
        fundLongLogoOpt = Some("allvue-long-logo.png")
      )
    ),
    DemoEnvironment.Debevoise -> Seq(
      AdditionalSetup.Dataroom,
      AdditionalSetup.WhiteLabel(
        fundSquareLogoOpt = Some("debevoise-square-logo.png"),
        fundLongLogoOpt = Some("debevoise-long-logo.png")
      )
    ),
    DemoEnvironment.PaulWeiss -> Seq(
      AdditionalSetup.Dataroom,
      AdditionalSetup.WhiteLabel(
        fundSquareLogoOpt = Some("paul-weiss-square-logo.png"),
        fundLongLogoOpt = Some("paul-weiss-long-logo.png")
      )
    ),
    DemoEnvironment.TMF -> Seq(
      AdditionalSetup.Dataroom,
      AdditionalSetup.WhiteLabel(
        fundSquareLogoOpt = Some("tmf-square-logo.png"),
        fundLongLogoOpt = Some("tmf-long-logo.png")
      )
    ),
    DemoEnvironment.Other -> Seq(
      AdditionalSetup.WhiteLabel(
        fundSquareLogoOpt = Some("default-logo.png"),
        fundLongLogoOpt = Some("default-logo.png")
      )
    )
  )

  sealed trait FundSubGroupDemoData derives CanEqual {
    def name: String
    def roleType: FundSubGroupRoleType
    def invitedMembers: Seq[FundSubGroupMemberInfo]
  }

  object FundSubGroupDemoData {

    final case class BusinessGroup(fundSubId: FundSubId) extends FundSubGroupDemoData {
      override val name = "Business"

      override val roleType: FundSubGroupRoleType = FundSubGroupRoleType.FundSubCustomRole()

      override val invitedMembers: Seq[FundSubGroupMemberInfo] = Seq(
        FundSubGroupMemberInfo(
          email = "<EMAIL>",
          firstName = "Jack",
          lastName = "Harries"
        ),
        FundSubGroupMemberInfo(
          email = "<EMAIL>",
          firstName = "Michelle",
          lastName = "Jones"
        )
      )

    }

  }

  sealed trait FundSubViewDemoData derives CanEqual {
    def name: String
    def visibleColumnIds: Seq[String]
    def availableColumnIds: Seq[String]
  }

  object FundSubViewDemoData {

    final case class BusinessDashboard(
      visibleColumnIds: Seq[String],
      availableColumnIds: Seq[String]
    ) extends FundSubViewDemoData {
      override def name: String = "Business Dashboard"
    }

  }

}
