// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.constants

import java.time.{Instant, LocalDate, Month}

import anduin.fundsub.simulator.model.*
import anduin.fundsub.utils.FormCommentUtils
import anduin.id.ModelIdRegistry
import anduin.id.dataextract.DataExtractProjectId
import anduin.id.form.*
import anduin.model.common.user.UserId
import anduin.model.id.AssetSessionId
import anduin.utils.DateTimeUtils

object DemoData {

  val submittedFilePath = "virginia.pdf"
  val acceptedFilePath = "hary.pdf"

  // new form ids
  val usCaymanFormId: FormId = ModelIdRegistry.parser
    .parseAs[FormId]("forfundsubuscayman00")
    .getOrElse(throw new RuntimeException("Invalid FormId"))

  val sessionId: AssetSessionId = AssetSessionId.FormBuilder("testsessionid")

  val usCaymanFormVersionId1: FormVersionId =
    ModelIdRegistry.parser
      .parseAs[FormVersionId](s"${usCaymanFormId.idString}.fveversio1")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))

  val usCaymanFormVersionId2: FormVersionId =
    ModelIdRegistry.parser
      .parseAs[FormVersionId](s"${usCaymanFormId.idString}.fveversio2")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))

  val usCaymanFormVersionId3: FormVersionId =
    ModelIdRegistry.parser
      .parseAs[FormVersionId](s"${usCaymanFormId.idString}.fveversio3")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))

  val usCaymanFormVersionId4: FormVersionId =
    ModelIdRegistry.parser
      .parseAs[FormVersionId](s"${usCaymanFormId.idString}.fveversio4")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))

  val fundSubFormForIaId: FormId = ModelIdRegistry.parser
    .parseAs[FormId]("forfundsubforiasim00")
    .getOrElse(throw new RuntimeException("Invalid FormId"))

  val fundSubFormForIaVersionId: FormVersionId =
    ModelIdRegistry.parser
      .parseAs[FormVersionId](s"${fundSubFormForIaId.idString}.fveversio0")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))

  val luxFormId: FormId = ModelIdRegistry.parser
    .parseAs[FormId]("forfundsubluxformsim")
    .getOrElse(throw new RuntimeException("Invalid FormId"))

  val luxFormVersionId: FormVersionId =
    ModelIdRegistry.parser
      .parseAs[FormVersionId](s"${luxFormId.idString}.fveversio0")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))

  val lightLogicFormId: FormId = ModelIdRegistry.parser
    .parseAs[FormId]("forfslightlogicsim00")
    .getOrElse(throw new RuntimeException("Invalid FormId"))

  val lightLogicFormVersionId: FormVersionId =
    ModelIdRegistry.parser
      .parseAs[FormVersionId](s"${lightLogicFormId.idString}.fveversion")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))

  val lpTransferFormId: FormId = ModelIdRegistry.parser
    .parseAs[FormId]("forlptransfer0000000")
    .getOrElse(throw new RuntimeException("Invalid FormId"))

  val lpTransferFormVersionId: FormVersionId =
    ModelIdRegistry.parser
      .parseAs[FormVersionId](s"${lpTransferFormId.idString}.fveversion")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))

  val masterSideLetterFormId: FormId = ModelIdRegistry.parser
    .parseAs[FormId]("formastersideletter0")
    .getOrElse(throw new RuntimeException("Invalid FormId"))

  val masterSideLetterFormVersionId: FormVersionId =
    ModelIdRegistry.parser
      .parseAs[FormVersionId](s"${masterSideLetterFormId.idString}.fveversion")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))

  val openEndedFormId: FormId = ModelIdRegistry.parser
    .parseAs[FormId]("foropenended00000002")
    .getOrElse(throw new RuntimeException("Invalid FormId"))

  val openEndedFormVersionId: FormVersionId = ModelIdRegistry.parser
    .parseAs[FormVersionId](s"${openEndedFormId.idString}.fveversio0")
    .getOrElse(throw new RuntimeException("Invalid FormVersionId"))

  val ocrFormId: FormId = ModelIdRegistry.parser
    .parseAs[FormId]("forocr00000000000002")
    .getOrElse(throw new RuntimeException("Invalid FormId"))

  val ocrFormVersionId: FormVersionId = ModelIdRegistry.parser
    .parseAs[FormVersionId](s"${ocrFormId.idString}.fveversio0")
    .getOrElse(throw new RuntimeException("Invalid FormVersionId"))

  val taxW8BENFormId: FormVersionId = {
    ModelIdRegistry.parser
      .parseAs[FormVersionId]("forsimulatortax8ben0.fveversion")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))
  }

  val taxW8BENEFormId: FormVersionId = {
    ModelIdRegistry.parser
      .parseAs[FormVersionId]("forsimulatortax8bene.fveversion")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))
  }

  val taxW9NewFormId: FormVersionId = {
    ModelIdRegistry.parser
      .parseAs[FormVersionId]("forsimulatortax92024.fveversion")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))
  }

  val anduinDemoFormIId: FormVersionId = {
    ModelIdRegistry.parser
      .parseAs[FormVersionId]("forsimulatorolddemo1.fveversion")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))
  }

  val anduinDemoFormIIId: FormVersionId = {
    ModelIdRegistry.parser
      .parseAs[FormVersionId]("forsimulatorolddemo7.fveversion")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))
  }

  val anduinDemoTechFormIId: FormVersionId = {
    ModelIdRegistry.parser
      .parseAs[FormVersionId]("forsimulatorolddemo2.fveversion")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))
  }

  val anduinBlockchainFormId: FormVersionId = {
    ModelIdRegistry.parser
      .parseAs[FormVersionId]("forsimulatorolddemo3.fveversion")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))
  }

  val anduinFlagshipFormIId: FormVersionId = {
    ModelIdRegistry.parser
      .parseAs[FormVersionId]("forsimulatorolddemo4.fveversion")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))
  }

  val anduinFlagshipFormIIId: FormVersionId = {
    ModelIdRegistry.parser
      .parseAs[FormVersionId]("forsimulatorolddemo5.fveversion")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))
  }

  val anduinPartnersFormId: FormVersionId = {
    ModelIdRegistry.parser
      .parseAs[FormVersionId]("forsimulatorolddemo6.fveversion")
      .getOrElse(throw new RuntimeException("Invalid FormVersionId"))
  }

  val actorSampleUserId: UserId = UserId.defaultValue.get
  val lpSampleUserId: UserId = UserId("user-0000000000000000000000000000000000lp")
  val actorSampleEmail: String = "<EMAIL>"

  val dataExtractProjectId: DataExtractProjectId = ModelIdRegistry.parser
    .parseAs[DataExtractProjectId]("dep0123456789012")
    .getOrElse(throw new RuntimeException("Invalid DataExtractProjectId"))

  // To support auto-fill
  val DemoSampleFiles: SampleFormFiles = SampleFormFiles(
    eventFileMap = Map(
      fundSubFormForIaVersionId -> "fundsub-ia/Demo Data.json",
      usCaymanFormVersionId4 -> "us-cayman-form/Schaden and Zemlak.json",
      luxFormVersionId -> "Hills, Emard and Connelly (Lux LP auto-fill).json",
      taxW8BENFormId -> "Form W-8 BEN Rev. October 2021.json",
      taxW8BENEFormId -> "Form W-8 BENE Rev. October 2021.json",
      taxW9NewFormId -> "tax-w9-form/Form W-9 Rev. March 2024 Allen Family Office.json",
      lightLogicFormVersionId -> "standard-offering-with-light-logic-demo-orders/Genesis Capital Investments (LP Auto-fill).json",
      lpTransferFormVersionId -> "lptransfer/Nancy Vanwieren.json",
      masterSideLetterFormVersionId -> "mastersideletter/David Walton I.json",
      openEndedFormVersionId -> "open-ended-demo-orders/Hills, Emard and Connelly - Auto-fill.json"
    ),
    dataFile = Some("Old Wire Instruction Form Data.json"),
    pageSectionFile = Some("Old Wire Instruction Form Page Sections.json")
  )

  private val demoFormCommentData = Seq(
    FormCommentData(
      fieldAlias = "paragraph21",
      fieldDescription = "Nature of the Subscriber: *",
      tocSection = "Section I - General Information",
      comment = "Hi GP, can you please advise which type of investor type I should pick?"
    ),
    FormCommentData(
      fieldAlias = "newissuesyn",
      fieldDescription = "Do you want to participate in New Issues? *",
      tocSection = "Appendix C",
      comment = "Hi GP, I can't remember my choice for this in the last subscription. Can you please advise here again?"
    ),
    FormCommentData(
      fieldAlias = "paragraph21",
      fieldDescription = "Nature of the Subscriber: *",
      tocSection = "Section I - General Information",
      comment = "Please give investor some advices here!",
      isPublic = false
    ),
    FormCommentData(
      fieldAlias = "paragraph21",
      fieldDescription = "Nature of the Subscriber: *",
      tocSection = "Section I - General Information",
      comment = "OK, I'll need to double-check his profile.",
      isPublic = false
    ),
    FormCommentData(
      fieldAlias = "newissuesyn",
      fieldDescription = "Do you want to participate in New Issues? *",
      tocSection = "Appendix C",
      comment = "Please give investor some advices here!",
      isPublic = false
    ),
    FormCommentData(
      fieldAlias = "newissuesyn",
      fieldDescription = "Do you want to participate in New Issues? *",
      tocSection = "Appendix C",
      comment = "OK, I'll need to double-check his profile. cc " + FormCommentUtils.mentionUserTag(actorSampleUserId),
      isPublic = false
    )
  )

  private val demoRequestChangeCommentData = Seq(
    RequestChangeCommentData(
      lpEmailAddress = "<EMAIL>",
      comments = Seq(
        FormCommentData(
          fieldAlias = "whichfundarea",
          fieldDescription = "Are you investing in the Delaware or Cayman Island fund(s)?",
          tocSection = "Section I - General Information",
          comment =
            "Could you please review your commitment amount to qualify for this fund? Currently, we require at least $2,000,000.",
          isPublic = true
        ),
        FormCommentData(
          fieldAlias = "newissuesyn",
          fieldDescription = "Do you want to participate in New Issues? *",
          tocSection = "Appendix C",
          comment =
            """In your previous subscription to our fund I, you selected the Single Member LLC investor type.
              |If you invest from the same investment entity for this fund, please re-select that option.""".stripMargin,
          isPublic = true
        )
      )
    ),
    RequestChangeCommentData(
      lpEmailAddress = "<EMAIL>",
      comments = Seq(
        FormCommentData(
          fieldAlias = "selectfundscayman",
          fieldDescription = "Are you investing in the Delaware or Cayman Island fund(s)?",
          tocSection = "Section I - General Information",
          comment =
            "Following our latest discussion, all your investment amounts should go to Delaware One Fund. Could you please revise your answer here?",
          isPublic = true
        ),
        FormCommentData(
          fieldAlias = "beneficialowner_entities_subscribernature_entity",
          fieldDescription =
            """If the Subscriber is acting as a trustee, agent, representative or nominee
              |for a Beneficial Owner, please check the item that best describes the Beneficial Owner:""".stripMargin,
          tocSection = "Section I - General Information",
          comment = FormCommentUtils.mentionUserTag(lpSampleUserId) + " " +
            """Could you revise the answer to describe the Beneficial Owner
              |in this case to "None of the above is applicable"?""".stripMargin,
          isPublic = true
        )
      )
    )
  )

  // For flexible flows
  val demoFlexibleForm: DemoFormData = DemoFormData(
    formVersionIds = Seq(
      usCaymanFormVersionId1,
      usCaymanFormVersionId2,
      usCaymanFormVersionId3,
      usCaymanFormVersionId4
    ),
    formName = "Demo Flexible Form"
  )

  val demoFundSubFormForIa: DemoFormData = DemoFormData(
    formVersionIds = Seq(fundSubFormForIaVersionId),
    formName = "Demo Flexible Form V2"
  )
  // End

  val demoLuxForm: DemoFormData = DemoFormData(
    formVersionIds = Seq(luxFormVersionId),
    formName = "Demo Luxembourg Form"
  )

  val demoLightLogicForm: DemoFormData = DemoFormData(
    formVersionIds = Seq(lightLogicFormVersionId),
    formName = "Demo Fund V Form" // formName is used to auto-fill, be careful when modifying
  )

  val demoLpTransferForm: DemoFormData = DemoFormData(
    formVersionIds = Seq(lpTransferFormVersionId),
    formName = "LP Transfer Form"
  )

  val demoMasterSideLetterForm: DemoFormData = DemoFormData(
    formVersionIds = Seq(masterSideLetterFormVersionId),
    formName = "Master Side Letter Form"
  )

  val demoOpenEndedForm: DemoFormData = DemoFormData(
    formVersionIds = Seq(openEndedFormVersionId),
    formName = "Demo open-ended fund"
  )

  val demoOcrForm: DemoFormData = DemoFormData(
    formVersionIds = Seq(ocrFormVersionId),
    formName = "Demo OCR fund"
  )

  val demoW8BENTaxForm: DemoFormData = DemoFormData(
    formVersionIds = Seq(taxW8BENFormId),
    formName = "Form W-8 BEN Rev. October 2021"
  )

  val demoW8BENETaxForm: DemoFormData = DemoFormData(
    formVersionIds = Seq(taxW8BENEFormId),
    formName = "Form W-8BEN-E Rev. October 2021"
  )

  val demoW9TaxNewForm: DemoFormData = DemoFormData(
    formVersionIds = Seq(taxW9NewFormId),
    formName = "Form W-9 Rev. March 2024"
  )

  val anduinBlockChainForm: DemoFormData = DemoFormData(
    formVersionIds = Seq(anduinBlockchainFormId),
    formName = "Anduin Blockchain Form"
  )

  val anduinDemoFormI: DemoFormData = DemoFormData(
    formVersionIds = Seq(anduinDemoFormIId),
    formName = "Anduin Demo Form I"
  )

  val anduinDemoFormII: DemoFormData = DemoFormData(
    formVersionIds = Seq(anduinDemoFormIIId),
    formName = "Anduin Demo Form II"
  )

  val anduinDemoTechFormI: DemoFormData = DemoFormData(
    formVersionIds = Seq(anduinDemoTechFormIId),
    formName = "Anduin Demo Tech Form"
  )

  val anduinFlagshipFormI: DemoFormData = DemoFormData(
    formVersionIds = Seq(anduinFlagshipFormIId),
    formName = "Anduin Flagship Form I"
  )

  val anduinFlagshipFormII: DemoFormData = DemoFormData(
    formVersionIds = Seq(anduinFlagshipFormIIId),
    formName = "Anduin Flagship Form II"
  )

  val anduinPartnersForm: DemoFormData = DemoFormData(
    formVersionIds = Seq(anduinPartnersFormId),
    formName = "Anduin Partners Form"
  )

  private val UsDemoOrders = Seq(
    DemoOrder(
      name = "Adam Emmerich",
      email = "<EMAIL>",
      firmName = "Emmerich Group",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Emmerich Group.json",
            taxW9NewFormId -> "tax-w9-form/Form W-9 Rev. March 2024 Emmerich Group.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("3120000"),
          filled = Seq(97, 100),
          submitSupDocs = Some(100),
          signed = true
        )
      ),
      tags = Seq("Endowment fund", "International"),
      amlKycCommentThreads = Seq(
        AmlKycCommentThread(
          doctype = "A separate sheet regarding NFA membership for each beneficial owner",
          comments = Seq(
            AmlKycComment(
              comment = "It looks like the pages for two beneficial owners are missing. Can you check again, Adam?",
              commentorEmail = "<EMAIL>"
            )
          ),
          isPublic = true
        ),
        AmlKycCommentThread(
          doctype = "A separate sheet regarding NFA membership for each beneficial owner",
          comments = Seq(
            AmlKycComment(
              comment = "The pages for two beneficial owners are missing.",
              commentorEmail = "<EMAIL>"
            ),
            AmlKycComment(
              comment = "I'll let him know",
              commentorEmail = "<EMAIL>"
            )
          ),
          isPublic = false
        ),
        AmlKycCommentThread(
          doctype =
            "A legible copy of a current government issued photo ID (e.g., driver’s license or passport), etc.) for each authorized signer.",
          comments = Seq(
            AmlKycComment(
              comment =
                "My co-signer Grant is waiting for his new passport to come in the mail and his driver's license needs to be renewed.",
              commentorEmail = "<EMAIL>"
            )
          ),
          isPublic = true
        )
      ),
      sideLetterPackage = Option(
        SideLetterPackage(
          folderName = "Emmerich Group",
          versions = Seq(
            SideLetterVersion(
              gpUploaded = true,
              folderName = "v1",
              files = Seq(
                SideLetterFile(
                  filename = "Emmerich Group - SLA.docx"
                )
              )
            ),
            SideLetterVersion(
              gpUploaded = true,
              folderName = "v2",
              files = Seq(
                SideLetterFile(
                  filename = "Appendix.docx"
                ),
                SideLetterFile(
                  filename = "Emmerich Group - Redline.docx"
                ),
                SideLetterFile(
                  filename = "Emmerich Group - SLA.docx"
                )
              )
            ),
            SideLetterVersion(
              gpUploaded = true,
              folderName = "v3",
              files = Seq(
                SideLetterFile(
                  filename = "Emmerich Group - SLA.pdf",
                  isMainFile = true,
                  isSignedOffline = true
                ),
                SideLetterFile(
                  filename = "Emmerich Group - Redline.docx"
                ),
                SideLetterFile(
                  filename = "Appendix.pdf"
                ),
                SideLetterFile(
                  filename = "Appendix-signed.pdf",
                  isSignedOffline = true
                )
              ),
              isAgreed = true
            )
          )
        )
      )
    ),
    DemoOrder(
      name = "Ruth Dickens",
      email = "<EMAIL>",
      firmName = "Dicken Rollover Pension Trust",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Dicken Rollover Pension Trust.json",
            taxW9NewFormId -> "tax-w9-form/Form W-9 Rev. March 2024 Dicken Rollover Pension Trust.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("700000"),
          // this user is used for demoRequestChangeCommentData, progress.filled need to be non empty
          filled = Seq(40),
          submitSupDocs = Some(30)
        )
      ),
      tags = Seq("Family Office", "Delaware"),
      autoApprovedAmlKycDocs = true,
      isCommentingSupported = true,
      sideLetterPackage = Option(
        SideLetterPackage(
          folderName = "Dicken Rollover Pension Trust",
          versions = Seq(
            SideLetterVersion(
              gpUploaded = true,
              folderName = "v1",
              files = Seq(
                SideLetterFile(
                  filename = "Dicken Rollover Pension Trust - SLA.docx"
                )
              )
            ),
            SideLetterVersion(
              gpUploaded = true,
              folderName = "v2",
              files = Seq(
                SideLetterFile(
                  filename = "Dicken Rollover Pension Trust - SLA.docx"
                )
              )
            ),
            SideLetterVersion(
              gpUploaded = true,
              folderName = "v3",
              files = Seq(
                SideLetterFile(
                  filename = "Dicken Rollover Pension Trust - SLA.pdf",
                  isMainFile = true
                ),
                SideLetterFile(
                  filename = "Dicken Rollover Pension Trust - Redline.docx"
                ),
                SideLetterFile(
                  filename = "Appendix.docx"
                )
              ),
              isAgreed = true
            )
          )
        )
      )
    ),
    DemoOrder(
      name = "Faith Hemmings",
      email = "<EMAIL>",
      firmName = "Schaden and Zemlak",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Schaden and Zemlak.json",
            taxW8BENEFormId -> "Form W-8 BENE Rev. October 2021.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("700000"),
          filled = Seq(
            98,
            99,
            100
          ),
          submitSupDocs = Some(100),
          signed = true,
          approved = true,
          distributed = Some("1400000")
        )
      ),
      tags = Seq(
        "Individual",
        "Delaware",
        "Money Wired"
      ),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Rebecca Hills",
      email = "<EMAIL>",
      firmName = "Hills, Emard and Connelly",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Hills, Emard and Connelly.json",
            taxW9NewFormId -> "tax-w9-form/Form W-9 Rev. March 2024 Hills, Emard and Connelly.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("1300000")
        )
      ),
      tags = Seq("Individual", "Delaware"),
      collaborators = Seq(
        DemoOrderCollaborator(isDemoUser = true),
        DemoOrderCollaborator(
          email = "<EMAIL>",
          firstName = "Emma",
          lastName = "White"
        )
      ),
      autoApprovedAmlKycDocs = true,
      isMainLpDemo = true
    ),
    DemoOrder(
      name = "Rebecca Hills",
      email = "<EMAIL>",
      firmName = "Hills, Emard and Connelly",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Hills, Emard and Connelly (FDM Auto-prefill).json",
            taxW9NewFormId -> "tax-w9-form/Form W-9 Rev. March 2024 Hills, Emard and Connelly.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("2600000")
        )
      ),
      tags = Seq("Individual", "Delaware"),
      collaborators = Seq(
        DemoOrderCollaborator(isDemoUser = true),
        DemoOrderCollaborator(
          email = "<EMAIL>",
          firstName = "Emma",
          lastName = "White"
        )
      ),
      autoApprovedAmlKycDocs = true,
      isAdditionalLpDemo = true,
      isGpAutofilled = true
    ),
    DemoOrder(
      name = "Adam Emmerich",
      email = "<EMAIL>",
      firmName = "Emmerich Family Trust",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Watson 1990 Family Trust.json",
            taxW8BENEFormId -> "Form W-8 BENE Rev. October 2021.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("890000"),
          filled = Seq(99, 100),
          submitSupDocs = Some(80),
          signed = true
        )
      ),
      autoApprovedAmlKycDocs = true,
      tags = Seq("Individual", "International")
    ),
    DemoOrder(
      name = "Carl Paige",
      email = "<EMAIL>",
      firmName = "Gutmann, Kunde and Hodkiewicz",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Gutmann, Kunde and Hodkiewicz.json",
            taxW9NewFormId -> "tax-w9-form/Form W-9 Rev. March 2024 Hills, Emard and Connelly.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("2130000"),
          filled = Seq(100),
          submitSupDocs = Some(50),
          submittedForReview = true
        )
      ),
      autoApprovedAmlKycDocs = true,
      tags = Seq("Institutional", "International"),
      sideLetterPackage = Option(
        SideLetterPackage(
          folderName = "Gutmann, Kunde and Hodkiewicz",
          versions = Seq(
            SideLetterVersion(
              gpUploaded = true,
              folderName = "v1",
              files = Seq(
                SideLetterFile(
                  filename = "CARL PAIGE - SLA.docx"
                )
              )
            )
          )
        )
      )
    ),
    DemoOrder(
      name = "Joan Rees",
      email = "<EMAIL>",
      firmName = "Leuschke, Runolfsdottir and Hane",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Leuschke, Runolfsdottir and Hane.json",
            taxW8BENEFormId -> "Form W-8 BENE Rev. October 2021.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("5500000"),
          filled = Seq(97),
          submitSupDocs = Some(90)
        )
      ),
      autoApprovedAmlKycDocs = true,
      tags = Seq("Institutional", "Delaware")
    ),
    DemoOrder(
      name = "Rose Lambert",
      email = "<EMAIL>",
      firmName = "Kemmer Law Group",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Kemmer Law Group.json",
            taxW8BENFormId -> "Form W-8 BEN Rev. October 2021.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("300000"),
          filled = Seq(100),
          submitSupDocs = Some(100),
          signed = true
        )
      ),
      autoApprovedAmlKycDocs = true,
      tags = Seq("Institutional", "International")
    ),
    DemoOrder(
      name = "Elizabeth Miller",
      email = "<EMAIL>",
      firmName = "Jenkins Family Office",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Jenkin Family Office.json",
            taxW8BENFormId -> "Form W-8 BEN Rev. October 2021.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("3000000"),
          filled = Seq(
            96,
            97,
            99,
            100
          ),
          submitSupDocs = Some(100),
          signed = true,
          approved = true,
          distributed = Some("5000000")
        )
      ),
      autoApprovedAmlKycDocs = true,
      tags = Seq(
        "Pension fund",
        "Delaware",
        "Waiting on Wire"
      ),
      sideLetterPackage = Option(
        SideLetterPackage(
          folderName = "Jenkins Family Office",
          versions = Seq(
            SideLetterVersion(
              gpUploaded = true,
              folderName = "v1",
              files = Seq(
                SideLetterFile(
                  filename = "Jenkins Family Office - Executed.pdf",
                  isExecuted = true
                )
              ),
              isCompleted = true
            )
          )
        )
      )
    ),
    DemoOrder(
      name = "Jonathin Sundin",
      email = "<EMAIL>",
      firmName = "Huels-McGrath Investment Board",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Huels-McGrath Investment Board.json",
            taxW8BENFormId -> "Form W-8 BEN Rev. October 2021.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("450000"),
          filled = Seq(98, 100),
          submitSupDocs = Some(100),
          signed = true,
          approved = true
        )
      ),
      autoApprovedAmlKycDocs = true,
      tags = Seq(
        "Pension fund",
        "International",
        "Money Wired"
      )
    ),
    DemoOrder(
      name = "Robert Hudson",
      email = "<EMAIL>",
      firmName = "Hudson Foundation",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Hudson Foundation.json",
            taxW8BENFormId -> "Form W-8 BEN Rev. October 2021.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("850000"),
          filled = Seq(40),
          submitSupDocs = Some(40)
        )
      ),
      autoApprovedAmlKycDocs = true,
      tags = Seq("Pension fund", "Delaware")
    ),
    DemoOrder(
      name = "Jacob Manning",
      email = "<EMAIL>",
      firmName = "Manning & Kemmer Coorp",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Manning Kemmer Coorp.json",
            taxW8BENEFormId -> "Form W-8 BENE Rev. October 2021.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("1450000"),
          filled = Seq(70, 100),
          submitSupDocs = Some(100),
          signed = true,
          approved = true
        )
      ),
      closeIndex = 1,
      tags = Seq("Endowment fund", "International"),
      autoApprovedAmlKycDocs = false
    ),
    DemoOrder(
      name = "Ruth Dickens",
      email = "<EMAIL>",
      firmName = "Dickens Rollover IRA",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Dickens Rollover IRA.json",
            taxW9NewFormId -> "tax-w9-form/Form W-9 Rev. March 2024 Dicken Rollover Pension Trust.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("700000"),
          filled = Seq(100),
          submitSupDocs = Some(80)
        )
      ),
      tags = Seq("Endowment fund", "Delaware"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Dennis Allen",
      email = "<EMAIL>",
      firmName = "Allen Family Office",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Allen Family Office.json",
            taxW9NewFormId -> "tax-w9-form/Form W-9 Rev. March 2024 Allen Family Office.json"
          )
        )
      ),
      closeIndex = 1,
      progress = None,
      tags = Seq("Family Office", "International"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Fiona Jones",
      email = "<EMAIL>",
      firmName = "Berge Family Office",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "us-cayman-form/Berge Family Office.json",
            taxW9NewFormId -> "tax-w9-form/Form W-9 Rev. March 2024 Dicken Rollover Pension Trust.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("700000"),
          // this user is used for demoRequestChangeCommentData, progress.filled need to be non empty
          filled = Seq(100),
          submitSupDocs = Some(75),
          submittedForReview = true
        )
      ),
      tags = Seq("Family Office", "Delaware"),
      autoApprovedAmlKycDocs = true,
      amlKycCommentThreads = Seq(
        AmlKycCommentThread(
          doctype = "(iii) such other materials and information as the Fund Administrator may reasonably request.",
          comments = Seq(
            AmlKycComment(
              comment = "Can you clarify what exactly we need to provide here?",
              commentorEmail = "<EMAIL>"
            )
          ),
          isPublic = true
        ),
        AmlKycCommentThread(
          doctype = "W-9 form",
          comments = Seq(
            AmlKycComment(
              comment = "Can you upload the most recent tax form? this seems to be an older one.",
              commentorEmail = "<EMAIL>"
            )
          ),
          isPublic = true
        ),
        AmlKycCommentThread(
          doctype = "(i) organizational documents (e.g., articles and bylaws, trust agreement or partnership agreement)",
          comments = Seq(
            AmlKycComment(
              comment = "Is this the same partnership agreement I uploaded in your previous fund?",
              commentorEmail = "<EMAIL>"
            ),
            AmlKycComment(
              comment = "Yes, we can use that agreement. I retrieved it and uploaded it for you.",
              commentorEmail = "<EMAIL>"
            )
          ),
          isPublic = true
        ),
        AmlKycCommentThread(
          doctype = "(i) organizational documents (e.g., articles and bylaws, trust agreement or partnership agreement)",
          comments = Seq(
            AmlKycComment(
              comment = s"Can he use the same partnership agreement he uploaded in your previous fund? " +
                s"${FormCommentUtils.mentionUserTag(actorSampleUserId)}",
              commentorEmail = "<EMAIL>"
            ),
            AmlKycComment(
              comment = "Yes, he can.",
              commentorEmail = actorSampleEmail
            ),
            AmlKycComment(
              comment = "Thanks! I'll let him know.",
              commentorEmail = "<EMAIL>"
            )
          ),
          isPublic = false
        ),
        AmlKycCommentThread(
          doctype =
            "(ii) evidence of authority to invest in the Fund (e.g., resolutions) and list of authorized signers.",
          comments = Seq(
            AmlKycComment(
              comment = "Hi Fiona, the uploaded list seems to be missing another page. Can you double checks?",
              commentorEmail = "<EMAIL>"
            )
          ),
          isResolved = true,
          isPublic = true
        )
      ),
      sideLetterPackage = Option(
        SideLetterPackage(
          folderName = "Berge Family Office",
          versions = Seq(
            SideLetterVersion(
              gpUploaded = true,
              folderName = "v1",
              files = Seq(
                SideLetterFile(
                  filename = "Berge Family Office - SLA.docx"
                )
              )
            ),
            SideLetterVersion(
              gpUploaded = true,
              folderName = "v2",
              files = Seq(
                SideLetterFile(
                  filename = "Berge Family Office - SLA.pdf",
                  isMainFile = true
                ),
                SideLetterFile(
                  filename = "Berge Family Office - Redline.docx"
                ),
                SideLetterFile(
                  filename = "Appendix.docx"
                )
              )
            )
          )
        )
      )
    )
  )

  private val InvestorAccessFundOrders = Seq(
    DemoOrder(
      name = "Hills, Emard and Connelly",
      email = "<EMAIL>",
      firmName = "Hills, Emard and Connelly",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            fundSubFormForIaVersionId -> "fundsub-ia/Hills, Emard and Connelly.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("5000000")
        )
      ),
      collaborators = Seq(DemoOrderCollaborator(isDemoUser = true)),
      isMainLpDemo = true
    ),
    DemoOrder(
      name = "Emmerich Family Trust",
      email = "<EMAIL>",
      firmName = "Emmerich Family Trust",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            fundSubFormForIaVersionId -> "fundsub-ia/Emmerich Family Trust.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("5000000"),
          filled = Seq(70),
          submitSupDocs = Some(100),
          signed = true,
          approved = true,
          distributed = Some("5000000")
        )
      )
    ),
    DemoOrder(
      name = "Emmerich Corporation",
      email = "<EMAIL>",
      firmName = "Emmerich Corporation",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            fundSubFormForIaVersionId -> "fundsub-ia/Emmerich Corporation.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("10000000"),
          filled = Seq(100)
        )
      )
    ),
    DemoOrder(
      name = "Brooklyn Allen",
      email = "<EMAIL>",
      firmName = "Brooklyn Allen",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            fundSubFormForIaVersionId -> "fundsub-ia/Brooklyn Allen.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("12000000"),
          filled = Seq(100),
          submitSupDocs = Some(100),
          signed = true,
          approved = true,
          distributed = Some("12000000")
        )
      )
    ),
    DemoOrder(
      name = "Gutmann, Kunde and Hodkiewicz",
      email = "<EMAIL>",
      firmName = "Gutmann, Kunde and Hodkiewicz",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            fundSubFormForIaVersionId -> "fundsub-ia/Gutmann, Kunde and Hodkiewicz.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("8000000"),
          filled = Seq(
            100
          ),
          submitSupDocs = Some(100),
          signed = true,
          approved = true,
          distributed = Some("8000000")
        )
      )
    ),
    DemoOrder(
      name = "Jenkins Family Office",
      email = "<EMAIL>",
      firmName = "Jenkins Family Office",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            fundSubFormForIaVersionId -> "fundsub-ia/Jenkins Family Office.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("6500000"),
          filled = Seq(
            100
          )
        )
      )
    ),
    DemoOrder(
      name = "Rebecca Hills",
      email = "<EMAIL>",
      firmName = "Rebecca Hills",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            fundSubFormForIaVersionId -> "fundsub-ia/Rebecca Hills.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("2500000"),
          filled = Seq(
            100
          )
        )
      )
    )
  )

  private val AdditionalFundForFundDataOrders = Seq(
    DemoOrder(
      name = "Emmerich Group",
      email = "<EMAIL>",
      firmName = "Emmerich Group",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "fundsub-funddata/Emmerich Group.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("15000000"),
          filled = Seq(
            100
          ),
          submitSupDocs = Some(100),
          signed = true,
          approved = true,
          distributed = Some("15000000")
        )
      )
    ),
    DemoOrder(
      name = "Brooklyn Allen",
      email = "<EMAIL>",
      firmName = "Brooklyn Allen",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "fundsub-funddata/Brooklyn Allen.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("4000000"),
          filled = Seq(
            100
          )
        )
      )
    ),
    DemoOrder(
      name = "Berge Family Office",
      email = "<EMAIL>",
      firmName = "Berge Family Office",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "fundsub-funddata/Berge Family Office.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("10000000"),
          filled = Seq(
            100
          ),
          submitSupDocs = Some(100),
          signed = true,
          approved = true,
          distributed = Some("1000000")
        )
      )
    ),
    DemoOrder(
      name = "BlueSky Holdings",
      email = "<EMAIL>",
      firmName = "BlueSky Holdings",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "fundsub-funddata/BlueSky Holdings.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("7500000"),
          filled = Seq(
            100
          ),
          submitSupDocs = Some(100)
        )
      )
    ),
    DemoOrder(
      name = "Daiki Rikuto",
      email = "<EMAIL>",
      firmName = "Daiki Rikuto",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            usCaymanFormVersionId4 -> "fundsub-funddata/Daiki Rikuto.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("520000"),
          filled = Seq(
            100
          )
        )
      )
    )
  )

  private val LuxembourgDemoOrders = Seq(
    DemoOrder(
      name = "Andrew Elder",
      email = "<EMAIL>",
      firmName = "Andrew Elder",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(luxFormVersionId -> "Andrew Elder.json")
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("80000000"),
          filled = Seq(95, 100)
        )
      ),
      tags = Seq("Pension fund", "Delaware"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Chester Marinelli",
      email = "<EMAIL>",
      firmName = "Chester M Marinelli",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(luxFormVersionId -> "Chester M Marinelli.json")
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("5000000"),
          filled = Seq(100)
        )
      ),
      closeIndex = 1,
      tags = Seq("Endowment fund", "International"),
      collaborators = Seq(DemoOrderCollaborator(isDemoUser = true)),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Martin White",
      email = "<EMAIL>",
      firmName = "Erb Lumber LLC",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(luxFormVersionId -> "Erb Lumber LLC.json")
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("5000000"),
          filled = Seq(100)
        )
      ),
      tags = Seq("Endowment fund", "Delaware"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Georges Antoine",
      email = "<EMAIL>",
      firmName = "Georges Antoine",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(luxFormVersionId -> "Georges Antoine.json")
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("7000000"),
          filled = Seq(100),
          signed = true
        )
      ),
      closeIndex = 1,
      tags = Seq("Individual", "Delaware"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Benjamin Ramsdale",
      email = "<EMAIL>",
      firmName = "Hoar Investments S.A.",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(luxFormVersionId -> "Hoar Investments S.A.json")
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("25000000"),
          filled = Seq(100),
          signed = true,
          approved = true
        )
      ),
      tags = Seq("Institutional", "International"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Aaron Saliba",
      email = "<EMAIL>",
      firmName = "Jolene Foundation",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(luxFormVersionId -> "Jolene Foundation.json")
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("6000000"),
          filled = Seq(100)
        )
      ),
      closeIndex = 1,
      tags = Seq("Endowment fund", "International"),
      collaborators = Seq(DemoOrderCollaborator(isDemoUser = true)),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "William Holding",
      email = "<EMAIL>",
      firmName = "Lux Investment S.A.",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(luxFormVersionId -> "Lux Investment S.A.json")
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("25000000"),
          filled = Seq(100),
          signed = true,
          distributed = Some("25000000")
        )
      ),
      tags = Seq(
        "Endowment fund",
        "Delaware",
        "Money Wired"
      ),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Rebecca Hills",
      email = "<EMAIL>",
      firmName = "Hills, Emard and Connelly",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            taxW9NewFormId -> "tax-w9-form/Form W-9 Rev. March 2024 Hills, Emard and Connelly.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("1300000")
        )
      ),
      tags = Seq("Individual", "Delaware"),
      collaborators = Seq(
        DemoOrderCollaborator(isDemoUser = true),
        DemoOrderCollaborator(
          email = "<EMAIL>",
          firstName = "Emma",
          lastName = "White"
        )
      ),
      isMainLpDemo = true,
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Searlas Douffet",
      email = "<EMAIL>",
      firmName = "Searlas Douffet",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(luxFormVersionId -> "Searlas Douffet.json")
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("17000000"),
          filled = Seq(100),
          signed = true,
          approved = true
        )
      ),
      tags = Seq("Pension fund", "International"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Kieran Nelson",
      email = "<EMAIL>",
      firmName = "Sidney Foundation",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(luxFormVersionId -> "Sidney Foundation.json")
        )
      ),
      progress = Some(
        OrderProgress(
          joined = None
        )
      ),
      tags = Seq("Pension fund", "Delaware"),
      autoApprovedAmlKycDocs = true
    )
  )

  private val StandardOfferingDemoOrders = Seq(
    DemoOrder(
      name = "Rebecca Hills",
      email = "<EMAIL>",
      firmName = "Genesis Capital Investments",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            lightLogicFormVersionId -> "standard-offering-with-light-logic-demo-orders/Genesis Capital Investments (LP Auto-fill).json"
          )
        )
      ),
      closeIndex = 1,
      progress = None,
      collaborators = Seq(
        DemoOrderCollaborator(isDemoUser = true),
        DemoOrderCollaborator(
          email = "<EMAIL>",
          firstName = "Emma",
          lastName = "White"
        )
      ),
      tags = Seq("Delaware"),
      isMainLpDemo = true,
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Anna Hudson",
      email = "<EMAIL>",
      firmName = "Anna Hudson",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            lightLogicFormVersionId -> "standard-offering-with-light-logic-demo-orders/Anna Hudson.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("4000000"),
          filled = Seq(63),
          submitSupDocs = Some(40)
        )
      ),
      tags = Seq("Individual", "Delaware"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "David Walton",
      email = "<EMAIL>",
      firmName = "David Walton",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            lightLogicFormVersionId -> "standard-offering-with-light-logic-demo-orders/David Walton.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("4000000"),
          filled = Seq(63, 100),
          submitSupDocs = Some(100),
          signed = true,
          approved = true,
          distributed = Some("300000000")
        )
      ),
      collaborators = Seq(DemoOrderCollaborator(isDemoUser = true)),
      tags = Seq("Individual", "International"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Jennifer Watson",
      email = "<EMAIL>",
      firmName = "Jennifer Watson",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            lightLogicFormVersionId -> "standard-offering-with-light-logic-demo-orders/Jennifer Watson.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("7000000")
        )
      ),
      tags = Seq("Individual", "Delaware"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Robert Lambert",
      email = "<EMAIL>",
      firmName = "Irongate Venture Capital",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            lightLogicFormVersionId -> "standard-offering-with-light-logic-demo-orders/Irongate Venture Capital.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("2000000"),
          filled = Seq(100),
          submitSupDocs = Some(60),
          submittedForReview = true
        )
      ),
      tags = Seq("Delaware"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Jenny Yoo",
      email = "<EMAIL>",
      firmName = "Jenny Yoo",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            lightLogicFormVersionId -> "standard-offering-with-light-logic-demo-orders/Jenny Yoo.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some(""),
          filled = Seq(100),
          submitSupDocs = Some(100),
          signed = true
        )
      ),
      tags = Seq("Individual", "International"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Jennifer Connelly",
      email = "<EMAIL>",
      firmName = "Midas Capital",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            lightLogicFormVersionId -> "standard-offering-with-light-logic-demo-orders/Midas Capital.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("6000000"),
          filled = Seq(100),
          submitSupDocs = Some(100),
          signed = true,
          approved = true
        )
      ),
      tags = Seq("Delaware"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Ruth Dickens",
      email = "<EMAIL>",
      firmName = "Summit Capital",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            lightLogicFormVersionId -> "standard-offering-with-light-logic-demo-orders/Summit Capital.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("4000000"),
          filled = Seq(100),
          submitSupDocs = Some(80)
        )
      ),
      tags = Seq("Grantor Trust", "International"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Amani Xavier",
      email = "<EMAIL>",
      firmName = "Amani Xavier",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            lightLogicFormVersionId -> "standard-offering-with-light-logic-demo-orders/Amani Xavier.json"
          )
        )
      ),
      closeIndex = 1,
      progress = None,
      tags = Seq("Individual", "International"),
      autoApprovedAmlKycDocs = true
    )
  )

  private val LpTransferDemoOrders = Seq(
    DemoOrder(
      name = "David Walton",
      email = "<EMAIL>",
      firmName = "David Walton I",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            lpTransferFormVersionId -> "lptransfer/David Walton I.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("9000000"),
          filled = Seq(100),
          submitSupDocs = Some(100),
          signed = true,
          approved = true
        )
      ),
      collaborators = Seq(
        DemoOrderCollaborator(
          email = "<EMAIL>",
          firstName = "Eric",
          lastName = "Miller"
        )
      ),
      tags = Seq("New Investor", "Transfer from Eric Millers"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Logan Smith",
      email = "<EMAIL>",
      firmName = "Logan Smith",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            lpTransferFormVersionId -> "lptransfer/Logan Smith.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = None,
          filled = Seq(25),
          submitSupDocs = Some(100)
        )
      ),
      collaborators = Seq(
        DemoOrderCollaborator(
          email = "<EMAIL>",
          firstName = "Eric",
          lastName = "Miller"
        )
      ),
      tags = Seq("Existing Investor", "Transfer from Eric Millers")
    ),
    DemoOrder(
      name = "Harry Williams",
      email = "<EMAIL>",
      firmName = "Harry Williams",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            lpTransferFormVersionId -> "lptransfer/Harry Williams.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("800000"),
          filled = Seq(100),
          submitSupDocs = Some(100)
        )
      ),
      collaborators = Seq(
        DemoOrderCollaborator(
          email = "<EMAIL>",
          firstName = "Eric",
          lastName = "Miller"
        )
      ),
      tags = Seq("New Investor", "Transfer from Eric Millers"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Julien Houbin",
      email = "<EMAIL>",
      closeIndex = 1
    ),
    DemoOrder(
      name = "Nancy Vanwieren",
      email = "<EMAIL>",
      firmName = "Vanwieren Venture",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            lpTransferFormVersionId -> "lptransfer/Vanwieren Venture.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("9000000"),
          filled = Seq(100),
          submitSupDocs = Some(100),
          signed = true
        )
      ),
      collaborators = Seq(
        DemoOrderCollaborator(
          email = "<EMAIL>",
          firstName = "Logan",
          lastName = "Smith"
        )
      ),
      tags = Seq("Existing Investor", "Transfer from Logan Smith"),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Quynh Anh Pham",
      email = "<EMAIL>",
      closeIndex = 1
    ),
    DemoOrder(
      name = "Nancy Vanwieren",
      email = "<EMAIL>",
      firmName = "Nancy Vanwieren",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            lpTransferFormVersionId -> "lptransfer/Nancy Vanwieren.json"
          )
        )
      ),
      closeIndex = 1,
      collaborators = Seq(
        DemoOrderCollaborator(isDemoUser = true),
        DemoOrderCollaborator(
          email = "<EMAIL>",
          firstName = "Logan",
          lastName = "Smith"
        )
      ),
      tags = Seq("Existing Investor", "Transfer from Eric Millers"),
      isMainLpDemo = true
    )
  )

  private val MasterSideLetterDemoOrders = Seq(
    DemoOrder(
      name = "David Walton",
      email = "<EMAIL>",
      firmName = "David Walton I",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            masterSideLetterFormVersionId -> "mastersideletter/David Walton I.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("9000000"),
          filled = Seq(100),
          submitSupDocs = Some(100),
          signed = true,
          approved = true
        )
      ),
      collaborators = Seq(),
      autoApprovedAmlKycDocs = true
    ),
    DemoOrder(
      name = "Logan Smith",
      email = "<EMAIL>",
      firmName = "Logan Smith",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            masterSideLetterFormVersionId -> "mastersideletter/David Walton I.json"
          )
        )
      ),
      closeIndex = 1,
      progress = Some(
        OrderProgress(
          joined = Some("9000000"),
          filled = Seq(25),
          submitSupDocs = Some(100)
        )
      ),
      collaborators = Seq(
        DemoOrderCollaborator(
          email = "<EMAIL>",
          firstName = "Eric",
          lastName = "Miller"
        )
      )
    ),
    DemoOrder(
      name = "Harry Williams",
      email = "<EMAIL>",
      closeIndex = 1,
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            masterSideLetterFormVersionId -> "mastersideletter/David Walton I.json"
          )
        )
      ),
      progress = None,
      collaborators = Seq(
        DemoOrderCollaborator(
          email = "<EMAIL>",
          firstName = "Eric",
          lastName = "Miller"
        ),
        DemoOrderCollaborator(isDemoUser = true)
      ),
      isMainLpDemo = true
    ),
    DemoOrder(
      name = "Julien Houbin",
      email = "<EMAIL>",
      closeIndex = 1
    )
  )

  private val OpenEndedFundDemoOrders = Seq(
    DemoOrder(
      name = "Rebecca Hills",
      email = "<EMAIL>",
      firmName = "Hills, Emard and Connelly",
      progress = Some(
        OrderProgress(
          joined = Some("5000000")
        )
      ),
      collaborators = Seq(
        DemoOrderCollaborator(isDemoUser = true),
        DemoOrderCollaborator(
          email = "<EMAIL>",
          firstName = "Emma",
          lastName = "White"
        )
      ),
      isMainLpDemo = true
    ),
    DemoOrder(
      name = "Robert Lambert",
      email = "<EMAIL>",
      firmName = "Irongate Venture Capital",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            openEndedFormVersionId -> "open-ended-demo-orders/Irongate Venture Capital - Initial.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("5000000"),
          filled = Seq(100),
          signed = true,
          approved = true,
          distributed = Some("5000000")
        )
      )
    ),
    DemoOrder(
      name = "Robert Lambert",
      email = "<EMAIL>",
      firmName = "Irongate Venture Capital",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            openEndedFormVersionId -> "open-ended-demo-orders/Irongate Venture Capital - Additional.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("0"),
          filled = Seq(100),
          signed = true,
          approved = true,
          distributed = Some("3500000")
        )
      )
    ),
    DemoOrder(
      name = "Robert Lambert",
      email = "<EMAIL>",
      firmName = "Irongate Venture Capital",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            openEndedFormVersionId -> "open-ended-demo-orders/Irongate Venture Capital - Withdrawal.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("0"),
          filled = Seq(100)
        )
      )
    ),
    DemoOrder(
      name = "Ruth Dickens",
      email = "<EMAIL>",
      firmName = "Ruth Dickens",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            openEndedFormVersionId -> "open-ended-demo-orders/Ruth Dickens.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("3000000"),
          filled = Seq(100)
        )
      )
    ),
    DemoOrder(
      name = "Jenny Yoo",
      email = "<EMAIL>",
      firmName = "Jenny Yoo",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            openEndedFormVersionId -> "open-ended-demo-orders/Jenny Yoo - Initial.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("3000000"),
          filled = Seq(100),
          signed = true,
          approved = true
        )
      )
    ),
    DemoOrder(
      name = "Jenny Yoo",
      email = "<EMAIL>",
      firmName = "Jenny Yoo",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            openEndedFormVersionId -> "open-ended-demo-orders/Jenny Yoo - Withdrawal.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("0"),
          filled = Seq(100)
        )
      )
    ),
    DemoOrder(
      name = "David Walton",
      email = "<EMAIL>",
      firmName = "David Walton",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            openEndedFormVersionId -> "open-ended-demo-orders/David Walton - Additional.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("0"),
          filled = Seq(100),
          signed = true,
          approved = true
        )
      )
    ),
    DemoOrder(
      name = "Adam Emmerich",
      email = "<EMAIL>",
      firmName = "Emmerich Family Trust",
      sampleFiles = Some(
        SampleFormFiles(
          eventFileMap = Map(
            openEndedFormVersionId -> "open-ended-demo-orders/Emmerich Family Trust - Withdrawal.json"
          )
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Some("0"),
          filled = Seq(100)
        )
      )
    )
  )

  val DataExtractOrders = Seq(
    DemoOrder(
      name = "Harry Smith",
      email = "<EMAIL>",
      firmName = "ABC Ventures",
      orcData = Option(
        OcrOrderData(
          uploadedPdf =
            "/fundsub/samples/data-extract/PDF 3 - Demo Fund L.P. - Subscription Booklet - Ali Biggs - scanned handwritings.pdf"
        )
      )
    ),
    DemoOrder(
      name = "Harry Smith",
      email = "<EMAIL>",
      tags = Seq("Demo upload first time"),
      progress = Some(
        OrderProgress(
          joined = Option("100000"),
          submitSupDocs = Option(100)
        )
      )
    ),
    DemoOrder(
      name = "Callum Combes",
      email = "<EMAIL>",
      sampleFiles = Some(SampleFormFiles()),
      tags = Seq("Demo reupload subdoc"),
      orcData = Option(
        OcrOrderData(
          uploadedPdf =
            "/fundsub/samples/data-extract/PDF 1 - Demo Fund L.P. - Subscription Booklet - TechSolutions Inc. - Acrobat.pdf",
          formDataFile = "/fundsub/samples/data-extract/Tech Solutions.json",
          shouldMarkRequestComplete = true
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Option("100000"),
          submitSupDocs = Option(100)
        )
      )
    ),
    DemoOrder(
      name = "Callum Combes",
      email = "<EMAIL>",
      sampleFiles = Some(SampleFormFiles()),
      tags = Seq("Demo reupload subdoc"),
      orcData = Option(
        OcrOrderData(
          uploadedPdf =
            "/fundsub/samples/data-extract/PDF 1 - Demo Fund L.P. - Subscription Booklet - TechSolutions Inc. - Acrobat.pdf",
          formDataFile = "/fundsub/samples/data-extract/Tech Solutions.json",
          shouldMarkRequestComplete = true
        )
      ),
      progress = Some(
        OrderProgress(
          joined = Option("100000"),
          submitSupDocs = Option(100)
        )
      )
    ),
    DemoOrder(
      name = "Callum Combes",
      email = "<EMAIL>",
      tags = Seq("Demo review OCR"),
      orcData = Option(
        OcrOrderData(
          uploadedPdf =
            "/fundsub/samples/data-extract/PDF 1 - Demo Fund L.P. - Subscription Booklet - TechSolutions Inc. - Acrobat.pdf",
          formDataFile = "/fundsub/samples/data-extract/Tech Solutions.json"
        )
      )
    ),
    DemoOrder(
      name = "Callum Combes",
      email = "<EMAIL>",
      progress = Option(
        OrderProgress(
          distributed = Option("1000000")
        )
      ),
      orcData = Option(
        OcrOrderData(
          uploadedPdf =
            "/fundsub/samples/data-extract/PDF 1 - Demo Fund L.P. - Subscription Booklet - TechSolutions Inc. - Acrobat.pdf"
        )
      )
    )
  )

  val dataTemplateId: DataTemplateId = {
    ModelIdRegistry.parser
      .parseAs[DataTemplateId]("dtp00000000000000000")
      .getOrElse(throw new RuntimeException("Invalid DataTemplateId"))
  }

  val dataTemplateVersionId: DataTemplateVersionId = {
    ModelIdRegistry.parser
      .parseAs[DataTemplateVersionId](dataTemplateId.idString + "." + "dtv0000000")
      .getOrElse(throw new RuntimeException("Invalid DataTemplateVersionId"))
  }

  private val demoGpImportLpTemplateData = DemoTemplateVersionData(
    dataTemplateId,
    dataTemplateVersionId,
    "Demo GP Import Template",
    "demo_flexible_form_import_template.xlsx",
    templateHeaderStartCol = 1,
    templateHeaderStartRow = 7
  )

  val demoGpImportLpFormTemplateMappingId: FormTemplateMappingId = demoFlexibleForm.latestVersionIdOpt
    .flatMap { formVersionId =>
      ModelIdRegistry.parser
        .parseAs[FormTemplateMappingId](s"${formVersionId.idString}.ftm0000")
    }
    .getOrElse(throw new RuntimeException("Cannot parse demoGpImportLpFormTemplateMappingId"))

  val demoGpImportLpFormTemplateMappingVersionId: FormTemplateMappingVersionId =
    ModelIdRegistry.parser
      .parseAs[FormTemplateMappingVersionId](
        s"${demoGpImportLpFormTemplateMappingId.idString}.fmv00000"
      )
      .getOrElse(throw new RuntimeException("Cannot parse demoGpImportLpFormTemplateMappingVersionId"))

  private val demoGpImportLpFormTemplateMappingData = FormTemplateMappingData(
    templateData = Some(demoGpImportLpTemplateData),
    formTemplateMappingJsonFileName = "demo_flexible_form_import_mapping.json",
    mappingId = demoGpImportLpFormTemplateMappingId,
    mappingVersionId = demoGpImportLpFormTemplateMappingVersionId,
    mappingName = "Import mapping for Demo flexible form"
  )

  val allDemoTemplates: Seq[DemoTemplateVersionData] = Seq(demoGpImportLpTemplateData)

  val allFormTemplateMappings: Seq[FormTemplateMappingData] = Seq(demoGpImportLpFormTemplateMappingData)

  val SampleFundData: DemoFundData = DemoFundData(
    fundName = "Demo Fund",
    formData = Some(demoFlexibleForm),
    capitalId = "commitmentamountforsetup",
    formCommentData = demoFormCommentData,
    requestChangeCommentData = Seq.empty,
    dataRoomNameForIntegration = "Demo data room"
  )

  val UsCaymanFund: DemoFundData = SampleFundData.copy(
    fundName = "Nucleus Capital Fund",
    capitalId = "", // use form integration config
    demoOrders = UsDemoOrders,
    formCommentData = Seq.empty,
    requestChangeCommentData = demoRequestChangeCommentData,
    formTemplateMappingConfigOpt = Some(demoGpImportLpFormTemplateMappingData)
  )

  val InvestorAccessFund: DemoFundData = DemoFundData(
    fundName = "Nucleus Partners Fund",
    formData = Some(demoFundSubFormForIa),
    capitalId = "textbox",
    demoOrders = InvestorAccessFundOrders,
    dataRoomNameForIntegration = "Demo data room"
  )

  val AdditionalFundForFundData: DemoFundData = DemoFundData(
    fundName = "Nucleus Luxembourg Fund",
    formData = Some(demoFlexibleForm),
    capitalId = "commitmentamountforsetup",
    demoOrders = AdditionalFundForFundDataOrders,
    dataRoomNameForIntegration = "Demo data room"
  )

  val LuxFund: DemoFundData = SampleFundData.copy(
    fundName = "Demo Luxembourg Fund",
    formData = Some(demoLuxForm),
    capitalId = "amountofcapitalcommitment",
    demoOrders = LuxembourgDemoOrders
  )

  val StandardOfferingFund: DemoFundData = SampleFundData.copy(
    fundName = "Demo Fund V",
    formData = Some(demoLightLogicForm),
    capitalId = "capital_commitment",
    demoOrders = StandardOfferingDemoOrders
  )

  val LpTransferFund: DemoFundData = SampleFundData.copy(
    fundName = "Demo LP Transfer Fund",
    formData = Some(demoLpTransferForm),
    capitalId = "lp_transfer_capital_commitment",
    demoOrders = LpTransferDemoOrders
  )

  val MasterSideLetterFund: DemoFundData = SampleFundData.copy(
    fundName = "Demo Master Side Letter Fund",
    formData = Some(demoMasterSideLetterForm),
    capitalId = "master_side_letter_capital_commitment",
    demoOrders = MasterSideLetterDemoOrders
  )

  val OpenEndedFund: DemoFundData = SampleFundData.copy(
    fundName = "Demo Open-ended Fund",
    formData = Some(demoOpenEndedForm),
    capitalId = "", // use form integration config
    demoOrders = OpenEndedFundDemoOrders
  )

  val DataExtract: DemoFundData = SampleFundData.copy(
    fundName = "Nucleus Capital Fund",
    capitalId = "commitmentamountforsetup",
    formData = Some(demoOcrForm),
    demoOrders = DataExtractOrders,
    formCommentData = Seq.empty,
    requestChangeCommentData = demoRequestChangeCommentData,
    formTemplateMappingConfigOpt = Some(demoGpImportLpFormTemplateMappingData)
  )

  private val AllDemoFunds = Seq(
    UsCaymanFund,
    LuxFund,
    StandardOfferingFund,
    LpTransferFund,
    MasterSideLetterFund,
    OpenEndedFund
  )

  def getFundDataByFormName(formName: String): Option[DemoFundData] = {
    AllDemoFunds.find(_.formData.exists(_.formName == formName))
  }

  // when form change, change these as well
  private val AllSupportingFilesMap: Map[String, String] = Map(
    "A separate sheet to provide the full list of jurisdictions of tax residency" -> "Jurisdictions of Tax Residency.pdf",
    "Evidence proving that the Subscriber has voluntarily surrendered its U.S. citizenship" -> "Evidence of Voluntarily Surrendering U.S. Citizenship.pdf",
    "A separate sheet to provide the full list of Substantial U.S. Owners" -> "Substantial U.S. Owners.pdf",
    "A copy of Part IV-Controlling Persons for each controlling person" -> "Part IV-Controlling Persons.pdf",
    "A separate list of all Controlling Persons" -> "All Controlling Persons.pdf",
    "A legible copy of a current government issued photo ID (e.g., driver’s license or passport)" -> "Government Issued ID.pdf",
    "Proof of residential/legal address (e.g., recent utility bill, phone bill, etc.) for each subscriber" -> "Proof of Residential.pdf",
    "Accredited Investor Verification Letter (if any)." -> "Accredited Investor Verification Letter.pdf",
    "A legible copy of a current government issued photo ID (e.g., driver’s license or passport), etc.) for each authorized signer." -> "Government Issued ID.pdf",
    "A copy of the legislation or your charter that sets forth the scope of such defense." -> "Personal Legislation Charter.pdf",
    "A full list of all beneficial owners." -> "All Beneficial Owners.pdf",
    "(i) organizational documents (e.g., articles and bylaws, trust agreement or partnership agreement)" -> "Organizational documents.pdf",
    "(ii) evidence of authority to invest in the Fund (e.g., resolutions) and list of authorized signers." -> "Evidence of Authority.pdf",
    "(iii) such other materials and information as the Fund Administrator may reasonably request." -> "Other materials & information.pdf",
    "A list containing the names of the trust’s grantors and IRS Form W-9(s) for each grantor." -> "Trust's Grantors.pdf",
    "A separate sheet including the full list of all equity owners." -> "All Equity Owners.pdf",
    "A separate sheet including the full list of all grantors." -> "All Grantors.pdf",
    "A separate sheet including the full list of all participants." -> "All Participants.pdf",
    "A list of all beneficial owners with either Appendix A (for beneficial owners who are themselves individuals) or Appendix B (for beneficial owners who are themselves entities)." -> "All Beneficial Owners.pdf",
    "A completed Appendix A (for beneficial owners who are themselves individuals) or Appendix B (for beneficial owners who are themselves entities)." -> "Appendix A.pdf",
    "A completed Appendix A (for trustees or persons who are themselves individuals) or Appendix B (for trustees who are themselves entities) for each of these trustees or persons." -> "Appendix B.pdf",
    "Appendix A for Participant 1" -> "Appendix A.pdf",
    "Appendix A for Participant 2" -> "Appendix A.pdf",
    "Appendix A for Participant 3" -> "Appendix A.pdf",
    "Appendix A for Joint Investor 1" -> "Appendix A.pdf",
    "A full list of all owners with Appendix A or Appendix B that has opted in to this investment " -> "Owners.pdf",
    "A separate sheet regarding NFA membership for each beneficial owner" -> "NFA Memberships.pdf",
    "The Appropriate Tax Form" -> "Form W-9 Rev. March 2024.pdf",
    "Government-issued photo ID" -> "Government Issued ID.pdf",
    "Address (if not listed on photo ID)" -> "Proof of Residential.pdf",
    "A list of all authorized signatories" -> "All Authorised Signatories.pdf",
    "Driver's License/Passport of Authorized Signatories" -> "Government Issued ID.pdf",
    "A list of beneficial owners owning directly or indirectly 10% or more of the entity (including name, tax ID, address, date of birth, nationality and current occupation of each 10% beneficial owner). If none, please provide a statement confirming that there no beneficial owners owning directly or indirectly 10% or more the entity." -> "All Beneficial Owners.pdf",
    "A copy of a current valid driver’s license and/or passport for each of the entity’s beneficial owners owning 10% or more of the entity" -> "Government Issued ID.pdf"
  )

  private val SupportingDocWithTax: Map[String, Seq[FormVersionId]] = Map(
    "W-9 Form" -> Seq(taxW9NewFormId),
    "The Appropriate Form W-8" -> Seq(taxW8BENFormId, taxW8BENEFormId)
  )

  private val TaxFormToSupportingDoc: Map[FormVersionId, String] = Map(
    taxW9NewFormId -> "Form W-9 Rev. March 2024.pdf",
    taxW8BENFormId -> "Form W-8 BEN Rev. October 2021.pdf",
    taxW8BENEFormId -> "Form W-8 BENE Rev. October 2021.pdf"
  )

  val SampleSupportingDocument = "Sample Supporting Document.pdf"
  val SampleSideLetterFile = "Sample Side Letter.pdf"

  val Note1Map: Map[String, String] = Map[String, String](
    "<EMAIL>" -> "Fund counsel approved 6/12",
    "<EMAIL>" -> "Fund counsel approved 6/12",
    "<EMAIL>" -> "Fund counsel approved 6/12",
    "<EMAIL>" -> "Referred from Mr Mark Emmerich - COO Emmerich Group",
    "<EMAIL>" -> "Checking Benefit Plan Matters options"
  )

  val Note2Map: Map[String, String] = Map[String, String](
    "<EMAIL>" -> "AML complete",
    "<EMAIL>" -> "KYC received, verifying AML",
    "<EMAIL>" -> "Please certify ID and explain the differences between the address on ID and Adddress Verification Document",
    "<EMAIL>" -> "Line 4 W-9 form missing ZIP code",
    "<EMAIL>" -> "AML complete"
  )

  val NoteColumns: List[(String, String, Map[String, String])] =
    List(("Note 1", "General Note", Note1Map), ("Note 2", "Note for supporting documents", Note2Map))

  val DateTime1Map: Map[String, Instant] = Map[String, Instant](
    "<EMAIL>" -> LocalDate
      .of(
        2023,
        Month.JULY,
        3
      )
      .atStartOfDay(DateTimeUtils.defaultTimezone)
      .toInstant,
    "<EMAIL>" -> LocalDate
      .of(
        2023,
        Month.JULY,
        22
      )
      .atStartOfDay(DateTimeUtils.defaultTimezone)
      .toInstant,
    "<EMAIL>" -> LocalDate
      .of(
        2023,
        Month.JULY,
        16
      )
      .atStartOfDay(DateTimeUtils.defaultTimezone)
      .toInstant,
    "<EMAIL>" -> LocalDate
      .of(
        2023,
        Month.JULY,
        10
      )
      .atStartOfDay(DateTimeUtils.defaultTimezone)
      .toInstant,
    "<EMAIL>" -> LocalDate
      .of(
        2023,
        Month.JULY,
        9
      )
      .atStartOfDay(DateTimeUtils.defaultTimezone)
      .toInstant
  )

  val DateTimeColumns: List[(String, String, Map[String, Instant])] = List(
    ("Date to review", "Deadline to review sub-doc", DateTime1Map)
  )

  val SingleSelect1Map: Map[String, String] = Map[String, String](
    "<EMAIL>" -> SingleSelect1.Option1,
    "<EMAIL>" -> SingleSelect1.Option2,
    "<EMAIL>" -> SingleSelect1.Option1,
    "<EMAIL>" -> SingleSelect1.Option2,
    "<EMAIL>" -> SingleSelect1.Option2
  )

  val SingleSelectColumns: List[(String, String, Map[String, String])] = List(
    ("Country", "Country of jurisdiction", SingleSelect1Map)
  )

  val SingleSelect1Options: List[String] = List(SingleSelect1.Option1, SingleSelect1.Option2)

  private object SingleSelect1 {
    val Option1: String = "US"
    val Option2: String = "Canada"
  }

  val MultipleSelect1Map: Map[String, Seq[String]] = Map[String, Seq[String]](
    "<EMAIL>" -> Seq(
      MultipleSelect1.Option1,
      MultipleSelect1.Option4,
      MultipleSelect1.Option5
    ),
    "<EMAIL>" -> Seq(
      MultipleSelect1.Option2,
      MultipleSelect1.Option5
    ),
    "<EMAIL>" -> Seq(
      MultipleSelect1.Option3,
      MultipleSelect1.Option4,
      MultipleSelect1.Option5
    ),
    "<EMAIL>" -> Seq(
      MultipleSelect1.Option1,
      MultipleSelect1.Option2,
      MultipleSelect1.Option3,
      MultipleSelect1.Option5
    ),
    "<EMAIL>" -> Seq(
      MultipleSelect1.Option1
    )
  )

  val MultipleSelectColumns: List[(String, String, Map[String, Seq[String]])] = List(
    ("Person in charge", "Fund counsel in charge of the subscription", MultipleSelect1Map)
  )

  val MultipleSelect1Options: List[String] = List(
    MultipleSelect1.Option1,
    MultipleSelect1.Option2,
    MultipleSelect1.Option3,
    MultipleSelect1.Option4,
    MultipleSelect1.Option5
  )

  private object MultipleSelect1 {
    val Option1: String = "Kate M"
    val Option2: String = "Anna Ng"
    val Option3: String = "Charlie"
    val Option4: String = "John"
    val Option5: String = "Edwards"
  }

  def getDemoSupportingFile(supportFileName: String, order: DemoOrder): Seq[DemoSupportingFileResult] = {
    SupportingDocWithTax
      .get(supportFileName)
      .fold {
        Seq(
          DemoSupportingFileResult(
            submitName = AllSupportingFilesMap.getOrElse(supportFileName, s"$supportFileName.pdf")
          )
        )
      } { taxFormVersionIds =>
        taxFormVersionIds
          .filter { taxFormVersionId =>
            order.sampleFiles.exists(_.eventFileMap.contains(taxFormVersionId))
          }
          .flatMap { taxFormVersionId =>
            TaxFormToSupportingDoc.get(taxFormVersionId).map { submitName =>
              DemoSupportingFileResult(
                submitName = submitName
              )
            }
          }
      }
  }

  final case class DemoSupportingFileResult(
    submitName: String
  ) extends AnyVal

  final case class DemoProps(
    isDemo: Boolean = false,
    formVersionIdOpt: Option[FormVersionId] = None,
    formNameOpt: Option[String] = None,
    firmNameOpt: Option[String] = None
  )

  val formUpdateNotes: Map[FormVersionId, String] = Map(
    usCaymanFormVersionId1 -> "First version",
    usCaymanFormVersionId2 -> "Add a few more questions",
    usCaymanFormVersionId3 -> "Update the footnote in the U.S. Person question",
    usCaymanFormVersionId4 -> "Enable multi-fund-currency"
  )

  val usInvestorGroupName = "US investors"
  val euInvestorGroupName = "EU investors"

  val investorGroups: Seq[String] = Seq(usInvestorGroupName, euInvestorGroupName)

}
